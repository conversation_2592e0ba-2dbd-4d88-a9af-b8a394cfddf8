#!/usr/bin/env python3
"""
评估指标实现模块（中文场景）

基于字符 n-gram 的 Precision、Recall、F1 指标进行评估。
"""

from typing import List, Dict, Any
from collections import Counter
import re


class IRMetrics:
    """
    评估指标计算器（字符 n-gram）

    计算基于字符 n-gram 重叠的 Precision、Recall、F1。
    """

    def __init__(self):
        """初始化评估器"""
        # 中文及常见标点（含中英文）统一替换为空格，再压缩空格
        punct_class = "[\u3000\u3001\u3002\uFF0C\uFF0E\uFF1F\uFF01\uFF1B\uFF1A\u201C\u201D\u2018\u2019\uFF08\uFF09\u300A\u300B\u2014\u2026\u00B7\-]"
        ascii_punct_class = "[\.,!?;:\\\"'()\[\]{}<>/@#\$%\^&\*_+=~`|\\\\]"
        pattern = "\\s+|" + punct_class + "+|" + ascii_punct_class + "+"
        self._punct_pattern = re.compile(pattern, re.UNICODE)

    def _normalize_text(self, text: str) -> str:
        """基础归一化：小写、去除空白与中英文标点，保留中文与英文数字。"""
        if not text:
            return ""
        text = text.lower()
        # 将各种空白与标点统一去除为单个空格，再压缩多空格
        text = self._punct_pattern.sub(" ", text)
        text = re.sub(r"\s+", " ", text).strip()
        return text

    def _char_ngrams(self, text: str, n: int = 2) -> Counter:
        """按字符生成 n-gram 多重集（去空白后）。"""
        norm = self._normalize_text(text)
        # 去除空格以更贴近中文连续文本匹配
        compact = norm.replace(" ", "")
        if n <= 0 or not compact:
            return Counter()
        grams = [compact[i:i + n] for i in range(0, max(0, len(compact) - n + 1))]
        return Counter(grams)

    def _calculate_char_ngram_overlap(self, retrieved_content: str, reference_content: str, n: int = 2) -> Dict[str, float]:
        """基于字符 n-gram 的多重集重叠（默认 2-gram）。"""
        r_cnt = self._char_ngrams(retrieved_content, n)
        q_cnt = self._char_ngrams(reference_content, n)
        if not r_cnt or not q_cnt:
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0,
                'ngram_intersection': 0,
                'retrieved_ngram_count': sum(r_cnt.values()),
                'reference_ngram_count': sum(q_cnt.values())
            }
        inter_cnt = r_cnt & q_cnt
        inter = sum(inter_cnt.values())
        r_total = sum(r_cnt.values())
        q_total = sum(q_cnt.values())
        precision = inter / r_total if r_total > 0 else 0.0
        recall = inter / q_total if q_total > 0 else 0.0
        f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        return {
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'ngram_intersection': inter,
            'retrieved_ngram_count': r_total,
            'reference_ngram_count': q_total
        }

    def evaluate(self, retrieved_chunks: List[str], reference_content: str, ngram_n: int = 2) -> Dict[str, Any]:
        """
        计算字符 n-gram 指标。

        参数:
            retrieved_chunks: 检索到的分块列表
            reference_content: 参考答案内容
            ngram_n: 字符 n-gram 的 n，默认 2

        返回:
            Dict[str, Any]: precision/recall/f1
        """
        combined_retrieved = " ".join(retrieved_chunks) if retrieved_chunks else ""
        return self._calculate_char_ngram_overlap(combined_retrieved, reference_content, n=ngram_n)


class ComprehensiveEvaluator:
    """
    综合评估器（仅字符 n-gram）
    """

    def __init__(self):
        self.ir_metrics = IRMetrics()

    def evaluate(self, retrieved_chunks: List[str], reference_content: str) -> Dict[str, Any]:
        """执行评估，返回 precision/recall/f1。"""
        return self.ir_metrics.evaluate(retrieved_chunks, reference_content)
