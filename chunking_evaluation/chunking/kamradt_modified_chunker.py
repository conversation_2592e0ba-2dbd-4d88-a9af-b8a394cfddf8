"""
Kamradt改进版分块器模块

此脚本改编自 Greg Ka<PERSON>t 的分块笔记本
原始代码可在以下位置找到: https://github.com/FullStackRetrieval-com/RetrievalTutorials/blob/main/tutorials/LevelsOfTextSplitting/5_Levels_Of_Text_Splitting.ipynb

这个模块实现了基于语义相似度和平均块大小的智能文本分块算法。
它通过计算句子间的余弦距离来识别语义边界，并使用二分搜索来优化分割阈值，
以达到期望的平均块大小。
"""

# 导入必要的模块和类型
from typing import Optional  # 可选类型提示
from .base_chunker import BaseChunker  # 基础分块器类
from .recursive_token_chunker import RecursiveTokenChunker  # 递归分块器
from chunking_evaluation.utils import openai_token_count  # 工具函数
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function
from chromadb.api.types import (  # ChromaDB类型定义
    Embeddable,  # 可嵌入类型
    EmbeddingFunction,  # 嵌入函数类型
)

import numpy as np  # 数值计算库

class KamradtModifiedChunker(BaseChunker):
    """
    Kamradt改进版分块器类
    
    这个分块器基于语义相似度将文本分割成大约指定平均大小的块。
    它是对Greg Kamradt分块笔记本的改进，增加了平均块大小参数。
    
    算法原理:
    1. 将文本分割成句子并为每个句子创建上下文窗口
    2. 计算相邻句子间的余弦距离（语义差异）
    3. 使用二分搜索找到最优的相似度阈值
    4. 在距离大于阈值的位置进行分割
    
    主要特点:
    - 基于语义相似度进行智能分割
    - 自动调整阈值以达到目标平均块大小
    - 使用上下文窗口增强句子表示
    - 支持自定义embedding函数和长度计算函数
    
    算法优势:
    - 保持语义相关内容的连续性
    - 自动平衡块大小和语义完整性
    - 适应不同类型的文本内容
    
    属性:
        avg_chunk_size (int): 期望的平均块大小（token数），默认400
        min_chunk_size (int): 最小块大小（token数），默认50
        embedding_function (EmbeddingFunction[Embeddable], optional): 将文本转换为embedding的函数，默认使用OpenAI embedding函数
        length_function (function): 计算文本token数量的函数，默认使用`openai_token_count`
        splitter (RecursiveTokenChunker): 用于初步分割的递归分块器

    主要方法:
        combine_sentences(sentences, buffer_size=1):
            使用指定的缓冲区大小组合句子，创建富含上下文的句子组
            
        calculate_cosine_distances(sentences):
            使用embedding计算组合句子间的余弦距离
            
        split_text(text):
            基于计算的余弦距离和指定的平均块大小分割输入文本

    使用示例:
        chunker = KamradtModifiedChunker(avg_chunk_size=300)
        text = "您要分块的文本。"
        chunks = chunker.split_text(text)
    """
    def __init__(
        self, 
        avg_chunk_size:int=400, 
        min_chunk_size:int=50, 
        embedding_function: Optional[EmbeddingFunction[Embeddable]] = None, 
        length_function=openai_token_count
        ):
        """
        初始化Kamradt改进版分块器
        
        参数:
            avg_chunk_size (int, optional): 期望的平均块大小（token数），默认400
            min_chunk_size (int, optional): 最小块大小（token数），默认50
            embedding_function (EmbeddingFunction[Embeddable], optional): 获取文本embedding的函数，如果不提供则默认使用OpenAI的embedding函数
            length_function (function, optional): 计算文本token长度的函数，默认使用`openai_token_count`
            
        初始化过程:
            1. 创建递归分块器用于初步分割
            2. 设置embedding函数（如果未提供则使用默认函数）
            3. 保存配置参数
        """
        
        # 创建递归分块器用于将文本初步分割成小的句子片段
        # 使用最小块大小确保每个片段都比较小，便于后续的语义分析
        self.splitter = RecursiveTokenChunker(
            chunk_size=min_chunk_size,  # 使用最小块大小作为初步分割的目标
            chunk_overlap=0,  # 不设置重叠，避免重复内容影响语义分析
            length_function=length_function  # 使用指定的长度计算函数
            )
        
        # 保存平均块大小配置
        self.avg_chunk_size = avg_chunk_size
        
        # 设置embedding函数，如果未提供则使用默认的OpenAI函数
        if embedding_function is None:
            embedding_function = get_openai_embedding_function()
        self.embedding_function = embedding_function
        
        # 保存长度计算函数
        self.length_function = length_function

    def combine_sentences(self, sentences, buffer_size=1):
        """
        为每个句子创建包含上下文的组合句子
        
        这个方法为每个句子添加前后的上下文句子，创建更丰富的语义表示。
        通过包含邻近句子的信息，可以更准确地计算句子间的语义相似度。
        
        参数:
            sentences (List[dict]): 句子字典列表，每个字典包含'sentence'和'index'键
            buffer_size (int): 缓冲区大小，决定包含前后多少个句子，默认1
            
        返回:
            List[dict]: 更新后的句子列表，每个字典新增'combined_sentence'键
            
        算法逻辑:
            1. 遍历每个句子
            2. 收集当前句子前buffer_size个句子
            3. 添加当前句子
            4. 收集当前句子后buffer_size个句子
            5. 将所有句子组合成一个字符串
            
        示例:
            如果buffer_size=1，句子"B"的组合句子将是"A B C"
            这样可以为句子B提供更多的上下文信息
        """
        # Go through each sentence dict
        # 遍历每个句子字典
        for i in range(len(sentences)):

            # Create a string that will hold the sentences which are joined
            # 创建一个字符串来保存组合的句子
            combined_sentence = ''

            # Add sentences before the current one, based on the buffer size.
            # 根据缓冲区大小添加当前句子之前的句子
            for j in range(i - buffer_size, i):
                # Check if the index j is not negative (to avoid index out of range like on the first one)
                # 检查索引j是否为非负数（避免在第一个句子时出现索引越界）
                if j >= 0:
                    # Add the sentence at index j to the combined_sentence string
                    # 将索引j处的句子添加到组合句子字符串中
                    combined_sentence += sentences[j]['sentence'] + ' '

            # Add the current sentence
            # 添加当前句子
            combined_sentence += sentences[i]['sentence']

            # Add sentences after the current one, based on the buffer size
            # 根据缓冲区大小添加当前句子之后的句子
            for j in range(i + 1, i + 1 + buffer_size):
                # Check if the index j is within the range of the sentences list
                # 检查索引j是否在句子列表的范围内
                if j < len(sentences):
                    # Add the sentence at index j to the combined_sentence string
                    # 将索引j处的句子添加到组合句子字符串中
                    combined_sentence += ' ' + sentences[j]['sentence']

            # Then add the whole thing to your dict
            # Store the combined sentence in the current sentence dict
            # 将整个组合句子存储在当前句子字典中
            sentences[i]['combined_sentence'] = combined_sentence

        return sentences

    def calculate_cosine_distances(self, sentences):
        """
        计算相邻句子间的余弦距离
        
        这个方法计算每个句子与下一个句子之间的余弦距离，用于识别语义边界。
        余弦距离越大，表示两个句子在语义上差异越大，越可能是分割点。
        
        参数:
            sentences (List[dict]): 包含组合句子的句子字典列表
            
        返回:
            Tuple[List[float], List[dict]]: 
                - distances: 相邻句子间的余弦距离列表
                - sentences: 更新后的句子列表，每个字典新增'distance_to_next'键
                
        算法步骤:
            1. 分批计算所有句子的embedding向量
            2. 标准化embedding向量为单位向量
            3. 计算相似度矩阵
            4. 提取相邻句子间的余弦相似度
            5. 转换为余弦距离（1 - 相似度）
            
        批处理说明:
            使用批处理可以提高embedding计算效率，避免逐个计算的开销
        """
        BATCH_SIZE = 500  # 批处理大小，平衡内存使用和计算效率
        distances = []  # 存储余弦距离的列表
        embedding_matrix = None  # 存储所有embedding向量的矩阵
        
        # 分批处理句子以计算embedding
        for i in range(0, len(sentences), BATCH_SIZE):
            batch_sentences = sentences[i:i+BATCH_SIZE]  # 获取当前批次的句子
            # 提取组合句子文本
            batch_sentences = [sentence['combined_sentence'] for sentence in batch_sentences]
            # 计算当前批次的embedding
            embeddings = self.embedding_function(batch_sentences)

            # Convert embeddings list of lists to numpy array
            # 将embedding列表转换为numpy数组
            batch_embedding_matrix = np.array(embeddings)

            # Append the batch embedding matrix to the main embedding matrix
            # 将当前批次的embedding矩阵追加到主矩阵
            if embedding_matrix is None:
                embedding_matrix = batch_embedding_matrix  # 第一批直接赋值
            else:
                embedding_matrix = np.concatenate((embedding_matrix, batch_embedding_matrix), axis=0)  # 后续批次拼接

        # Normalize each vector to be a unit vector
        # 将每个向量标准化为单位向量，这样点积就等于余弦相似度
        norms = np.linalg.norm(embedding_matrix, axis=1, keepdims=True)  # 计算每个向量的模长
        embedding_matrix = embedding_matrix / norms  # 标准化

        # 计算相似度矩阵：标准化后的向量点积等于余弦相似度
        similarity_matrix = np.dot(embedding_matrix, embedding_matrix.T)
        
        # 计算相邻句子间的余弦距离
        for i in range(len(sentences) - 1):
            # Calculate cosine similarity
            # 从相似度矩阵中获取相邻句子的余弦相似度
            similarity = similarity_matrix[i, i + 1]
            
            # Convert to cosine distance
            # 转换为余弦距离：距离 = 1 - 相似度
            distance = 1 - similarity

            # Append cosine distance to the list
            # 将余弦距离添加到列表中
            distances.append(distance)

            # Store distance in the dictionary
            # 在句子字典中存储到下一个句子的距离
            sentences[i]['distance_to_next'] = distance

        # Optionally handle the last sentence
        # 可选：处理最后一个句子（当前未设置distance_to_next）
        # sentences[-1]['distance_to_next'] = None  # or a default value

        return distances, sentences

    def split_text(self, text):
        """
        基于语义相似度将输入文本分割成大约指定平均大小的块
        
        这是算法的主要入口点，实现了完整的Kamradt改进版分块流程：
        1. 初步分割文本
        2. 创建上下文丰富的句子表示
        3. 计算语义距离
        4. 使用二分搜索找到最优阈值
        5. 根据阈值进行分割
        
        参数:
            text (str): 待分割的输入文本
            
        返回:
            List[str]: 文本块列表
            
        算法流程详解:
            1. 文本预处理：使用递归分块器分割成句子
            2. 上下文增强：为每个句子添加邻近句子的上下文
            3. 语义分析：计算相邻句子间的余弦距离
            4. 阈值优化：使用二分搜索找到最优的分割阈值
            5. 文本重组：根据阈值将句子重新组合成块
        """
        
        # 步骤1: 使用递归分块器将文本分割成句子片段
        sentences_strips = self.splitter.split_text(text)

        # 步骤2: 为每个句子创建字典，包含句子内容和索引
        sentences = [{'sentence': x, 'index' : i} for i, x in enumerate(sentences_strips)]

        # 步骤3: 为每个句子创建包含上下文的组合句子
        # 使用buffer_size=3，即每个句子包含前后3个句子的上下文
        sentences = self.combine_sentences(sentences, 3)

        # 提取组合句子列表（用于调试或其他用途）
        combined_sentences = [x['combined_sentence'] for x in sentences]

        # 步骤4: 计算相邻句子间的余弦距离
        distances, sentences = self.calculate_cosine_distances(sentences)

        # 步骤5: 计算总token数和期望的分割数量
        total_tokens = sum(self.length_function(sentence['sentence']) for sentence in sentences)
        avg_chunk_size = self.avg_chunk_size  # 期望的平均块大小
        number_of_cuts = total_tokens // avg_chunk_size  # 计算需要的分割次数

        # 步骤6: 使用二分搜索找到最优的距离阈值
        # Define threshold limits
        # 定义阈值搜索范围
        lower_limit = 0.0  # 下限：最小可能的余弦距离
        upper_limit = 1.0  # 上限：最大可能的余弦距离

        # Convert distances to numpy array
        # 将距离列表转换为numpy数组以提高计算效率
        distances_np = np.array(distances)

        # Binary search for threshold
        # 二分搜索最优阈值
        while upper_limit - lower_limit > 1e-6:  # 精度控制：当搜索范围小于1e-6时停止
            threshold = (upper_limit + lower_limit) / 2.0  # 计算中点作为候选阈值
            # 计算超过当前阈值的距离点数量
            num_points_above_threshold = np.sum(distances_np > threshold)
            
            # 根据分割点数量调整搜索范围
            if num_points_above_threshold > number_of_cuts:
                # 如果分割点太多，提高阈值（缩小搜索下限）
                lower_limit = threshold
            else:
                # 如果分割点太少，降低阈值（缩小搜索上限）
                upper_limit = threshold

        # 步骤7: 找到所有超过最优阈值的分割点
        indices_above_thresh = [i for i, x in enumerate(distances) if x > threshold] 
        
        # 步骤8: 根据分割点重新组合句子成块
        # Initialize the start index
        # 初始化起始索引
        start_index = 0

        # Create a list to hold the grouped sentences
        # 创建列表来保存分组的句子
        chunks = []

        # Iterate through the breakpoints to slice the sentences
        # 遍历分割点来切分句子
        for index in indices_above_thresh:
            # The end index is the current breakpoint
            # 结束索引是当前的分割点
            end_index = index

            # Slice the sentence_dicts from the current start index to the end index
            # 从当前起始索引到结束索引切分句子字典
            group = sentences[start_index:end_index + 1]
            # 将分组中的所有句子合并成一个文本块
            combined_text = ' '.join([d['sentence'] for d in group])
            chunks.append(combined_text)
            
            # Update the start index for the next group
            # 更新下一组的起始索引
            start_index = index + 1

        # The last group, if any sentences remain
        # 处理最后一组（如果还有剩余句子）
        if start_index < len(sentences):
            combined_text = ' '.join([d['sentence'] for d in sentences[start_index:]])
            chunks.append(combined_text)

        return chunks