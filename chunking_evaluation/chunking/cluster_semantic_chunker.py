"""
聚类语义分块器模块

这个模块实现了基于聚类和语义相似度的文本分块功能。
它使用embedding向量计算句子之间的语义相似度，然后通过动态规划算法
找到最优的文本分割点，确保语义相关的内容被分组在同一个块中。
"""

# 导入必要的模块和类型
from .base_chunker import BaseChunker  # 基础分块器类
from typing import List  # 类型提示

import numpy as np  # 数值计算库，用于矩阵运算
import tiktoken  # OpenAI的token计算库
from chunking_evaluation.chunking import RecursiveTokenChunker  # 递归分块器

# 导入工具函数
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function
from chunking_evaluation.utils import openai_token_count

class ClusterSemanticChunker(BaseChunker):
    """
    聚类语义分块器类
    
    这个类实现了基于语义相似度聚类的文本分块算法。它的核心思想是：
    1. 首先将文本分割成小的句子片段
    2. 计算每个句子的embedding向量
    3. 构建句子间的相似度矩阵
    4. 使用动态规划算法找到最优的聚类分割点
    5. 将语义相关的句子聚合成文本块
    
    主要特点:
    - 保持语义相关内容的连续性
    - 使用embedding向量进行语义相似度计算
    - 通过动态规划优化分割策略
    - 支持自定义embedding函数和块大小
    
    算法优势:
    - 比简单的固定长度分割更智能
    - 保持语义完整性
    - 适用于需要语义连贯性的应用场景
    
    属性:
        splitter (RecursiveTokenChunker): 用于初步分割的递归分块器
        _chunk_size (int): 最大块大小
        max_cluster (int): 最大聚类大小（块数量限制）
        embedding_function: 用于计算文本embedding的函数
    """
    
    def __init__(self, embedding_function=None, max_chunk_size=400, min_chunk_size=50, length_function=openai_token_count):
        """
        初始化聚类语义分块器
        
        参数:
            embedding_function: 用于计算文本embedding的函数，默认使用OpenAI的embedding
            max_chunk_size (int): 最大块大小（以token计），默认400
            min_chunk_size (int): 最小块大小（以token计），默认50
            length_function: 用于计算文本长度的函数，默认使用openai_token_count
            
        初始化过程:
            1. 创建递归分块器用于初步分割
            2. 设置embedding函数（如果未提供则使用默认的OpenAI函数）
            3. 计算最大聚类数量（基于块大小比例）
        """
        # 创建递归分块器，用于将文本初步分割成小的句子片段
        # 这里使用最小块大小，确保每个片段都比较小，便于后续聚类
        self.splitter = RecursiveTokenChunker(
            chunk_size=min_chunk_size,  # 使用最小块大小作为初步分割的目标
            chunk_overlap=0,  # 不设置重叠，避免重复内容
            length_function=openai_token_count,  # 使用OpenAI的token计数函数
            separators = ["\n\n", "\n", ".", "?", "!", " ", ""]  # 分隔符优先级列表
            )
        
        # 设置embedding函数
        if embedding_function is None:
            embedding_function = get_openai_embedding_function()  # 使用默认的OpenAI embedding函数
        
        # 保存配置参数
        self._chunk_size = max_chunk_size  # 最大块大小
        self.max_cluster = max_chunk_size//min_chunk_size  # 计算最大聚类数量
        self.embedding_function = embedding_function  # 保存embedding函数
        
    def _get_similarity_matrix(self, embedding_function, sentences):
        """
        计算句子间的相似度矩阵
        
        这个方法为所有句子计算embedding向量，然后构建相似度矩阵。
        为了处理大量句子，使用批处理方式计算embedding以提高效率。
        
        参数:
            embedding_function: 用于计算embedding的函数
            sentences (List[str]): 句子列表
            
        返回:
            np.ndarray: 句子间的相似度矩阵（N×N，N为句子数量）
            
        算法步骤:
            1. 分批处理句子以避免内存问题
            2. 为每批句子计算embedding向量
            3. 将所有embedding向量合并成矩阵
            4. 计算点积得到相似度矩阵
        """
        BATCH_SIZE = 500  # 批处理大小，避免一次处理过多句子导致内存问题
        N = len(sentences)  # 句子总数
        embedding_matrix = None  # 初始化embedding矩阵
        
        # 分批处理句子
        for i in range(0, N, BATCH_SIZE):
            batch_sentences = sentences[i:i+BATCH_SIZE]  # 获取当前批次的句子
            embeddings = embedding_function(batch_sentences)  # 计算当前批次的embedding

            # Convert embeddings list of lists to numpy array
            # 将embedding列表转换为numpy数组
            batch_embedding_matrix = np.array(embeddings)

            # Append the batch embedding matrix to the main embedding matrix
            # 将当前批次的embedding矩阵追加到主矩阵中
            if embedding_matrix is None:
                embedding_matrix = batch_embedding_matrix  # 第一批直接赋值
            else:
                embedding_matrix = np.concatenate((embedding_matrix, batch_embedding_matrix), axis=0)  # 后续批次进行拼接

        # 计算相似度矩阵：使用点积计算embedding向量间的相似度
        # 结果是N×N的对称矩阵，其中element[i,j]表示句子i和句子j的相似度
        similarity_matrix = np.dot(embedding_matrix, embedding_matrix.T)

        return similarity_matrix

    def _calculate_reward(self, matrix, start, end):
        """
        计算指定范围内的聚类奖励值
        
        这个方法计算从start到end范围内所有句子对的相似度总和，
        作为将这些句子聚类在一起的"奖励"。奖励值越高，
        说明这些句子在语义上越相关，越适合聚类在一起。
        
        参数:
            matrix (np.ndarray): 相似度矩阵
            start (int): 起始句子索引
            end (int): 结束句子索引
            
        返回:
            float: 聚类奖励值（子矩阵所有元素的和）
            
        计算逻辑:
            提取子矩阵并计算所有元素的和，这代表了该范围内
            所有句子对之间的总相似度
        """
        # 提取指定范围的子矩阵
        sub_matrix = matrix[start:end+1, start:end+1]
        # 返回子矩阵所有元素的和作为奖励值
        return np.sum(sub_matrix)

    def _optimal_segmentation(self, matrix, max_cluster_size, window_size=3):
        """
        使用动态规划算法找到最优的文本分割点
        
        这是算法的核心部分，使用动态规划来找到最优的句子聚类方案。
        目标是最大化总的聚类奖励值，同时满足聚类大小限制。
        
        参数:
            matrix (np.ndarray): 句子间的相似度矩阵
            max_cluster_size (int): 单个聚类的最大大小
            window_size (int): 窗口大小参数（当前未使用）
            
        返回:
            List[Tuple[int, int]]: 聚类列表，每个元素是(start, end)表示一个聚类的范围
            
        算法步骤:
            1. 对相似度矩阵进行标准化处理
            2. 使用动态规划计算最优分割方案
            3. 回溯构建最终的聚类列表
            
        动态规划状态:
            dp[i] = 以第i个句子结尾的最优聚类方案的总奖励值
            segmentation[i] = 以第i个句子结尾的聚类的起始位置
        """
        # 计算相似度矩阵的均值，用于标准化
        mean_value = np.mean(matrix[np.triu_indices(matrix.shape[0], k=1)])
        matrix = matrix - mean_value  # Normalize the matrix - 标准化矩阵
        np.fill_diagonal(matrix, 0)  # Set diagonal to 0 to avoid trivial solutions - 将对角线设为0避免平凡解

        n = matrix.shape[0]  # 句子数量
        dp = np.zeros(n)  # 动态规划数组：dp[i]表示以第i个句子结尾的最优方案的总奖励
        segmentation = np.zeros(n, dtype=int)  # 分割点数组：记录每个位置对应的聚类起始点

        # 动态规划主循环
        for i in range(n):
            # 尝试不同的聚类大小
            for size in range(1, max_cluster_size + 1):
                if i - size + 1 >= 0:  # 确保起始位置有效
                    # local_density = calculate_local_density(matrix, i, window_size)
                    # 计算当前聚类的奖励值
                    reward = self._calculate_reward(matrix, i - size + 1, i)
                    
                    # Adjust reward based on local density
                    # 调整奖励值：加上前面部分的最优奖励
                    adjusted_reward = reward
                    if i - size >= 0:
                        adjusted_reward += dp[i - size]  # 加上前面部分的最优解
                    
                    # 如果当前方案更优，更新动态规划数组
                    if adjusted_reward > dp[i]:
                        dp[i] = adjusted_reward
                        segmentation[i] = i - size + 1  # 记录聚类起始位置

        # 回溯构建聚类列表
        clusters = []
        i = n - 1
        while i >= 0:
            start = segmentation[i]  # 获取当前聚类的起始位置
            clusters.append((start, i))  # 添加聚类范围
            i = start - 1  # 移动到前一个聚类的结束位置

        clusters.reverse()  # 反转列表以获得正确的顺序
        return clusters
        
    def split_text(self, text: str) -> List[str]:
        """
        将文本分割成语义相关的文本块
        
        这是对外的主要接口，实现了完整的聚类语义分块流程。
        
        参数:
            text (str): 待分割的原始文本
            
        返回:
            List[str]: 分割后的文本块列表，每个块包含语义相关的内容
            
        算法流程:
            1. 使用递归分块器将文本分割成句子
            2. 计算句子间的相似度矩阵
            3. 使用动态规划找到最优聚类方案
            4. 根据聚类结果合并句子生成最终文本块
        """
        # 步骤1: 将文本分割成句子
        sentences = self.splitter.split_text(text)

        # 步骤2: 计算句子间的相似度矩阵
        similarity_matrix = self._get_similarity_matrix(self.embedding_function, sentences)

        # 步骤3: 使用动态规划找到最优聚类方案
        clusters = self._optimal_segmentation(similarity_matrix, max_cluster_size=self.max_cluster)

        # 步骤4: 根据聚类结果合并句子生成文本块
        docs = [' '.join(sentences[start:end+1]) for start, end in clusters]

        return docs