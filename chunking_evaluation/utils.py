"""
文本分块评估工具模块

这个模块提供了文本搜索、匹配和处理的核心工具函数。
主要用于支持文本分块评估过程中的各种文本操作需求。

主要功能：
1. 容错文本搜索 - 处理空白字符变化的文本匹配
2. 严格文档搜索 - 多层次的文本匹配策略
3. 嵌入函数获取 - OpenAI嵌入服务的便捷接口
4. Token计数 - 精确的文本token数量计算
5. 编程语言枚举 - 支持的编程语言类型定义
"""

from enum import Enum
import re
from fuzzywuzzy import fuzz
from fuzzywuzzy import process
import os
from chromadb.utils import embedding_functions
import tiktoken

def find_query_despite_whitespace(document, query):
    """
    在文档中查找查询文本，忽略空白字符的差异
    
    这个函数能够处理由于格式化、换行或空格变化导致的文本匹配问题。
    它通过正则表达式创建一个灵活的匹配模式，允许单词之间有任意的空白字符。
    
    参数:
        document (str): 要搜索的文档文本
        query (str): 要查找的查询文本
        
    返回:
        tuple或None: 如果找到匹配，返回(匹配文本, 开始位置, 结束位置)；否则返回None
        
    示例:
        document = "这是一个\n测试文档"
        query = "这是一个 测试文档"
        result = find_query_despite_whitespace(document, query)
        # 返回: ("这是一个\n测试文档", 0, 7)
    """
    # 标准化查询中的空格和换行符，将多个连续空白字符替换为单个空格
    normalized_query = re.sub(r'\s+', ' ', query).strip()
    
    # 从标准化的查询创建正则表达式模式，允许单词之间有任意空白字符
    # re.escape确保特殊字符被正确转义
    pattern = r'\s*'.join(re.escape(word) for word in normalized_query.split())
    
    # 编译正则表达式，忽略大小写并在文档中搜索
    regex = re.compile(pattern, re.IGNORECASE)
    match = regex.search(document)
    
    if match:
        # 如果找到匹配，返回匹配的文本和位置信息
        return document[match.start(): match.end()], match.start(), match.end()
    else:
        return None

def rigorous_document_search(document: str, target: str):
    """
    在文档中执行严格的目标字符串搜索
    
    这个函数实现了多层次的文本搜索策略，能够处理空白字符变化、语法改变
    和其他轻微的文本变更。搜索策略按优先级依次执行：
    1. 精确匹配 - 直接查找完全相同的文本
    2. 容错匹配 - 忽略空白字符差异的匹配
    3. 模糊匹配 - 基于句子级别的语义相似度匹配
    
    参数:
        document (str): 要搜索的文档文本
        target (str): 要查找的目标字符串
        
    返回:
        tuple或None: 如果找到匹配，返回(最佳匹配文本, 开始位置, 结束位置)；
                    如果没有找到匹配，返回None
                    
    搜索策略详解:
        1. 首先尝试精确匹配，这是最快且最准确的方法
        2. 如果精确匹配失败，使用容错搜索处理空白字符变化
        3. 最后使用模糊匹配，基于句子分割和相似度评分
        
    示例:
        document = "这是一个测试文档。包含多个句子。"
        target = "测试文档"
        result = rigorous_document_search(document, target)
        # 返回: ("测试文档", 4, 8)
    """
    # 预处理：如果目标字符串以句号结尾，移除句号以提高匹配成功率
    if target.endswith('.'):
        target = target[:-1]
    
    # 第一层：尝试精确匹配
    # 这是最快的方法，适用于文本完全一致的情况
    if target in document:
        start_index = document.find(target)
        end_index = start_index + len(target)
        return target, start_index, end_index
    else:
        # 第二层：容错搜索，处理空白字符变化
        # 适用于格式化导致的空白字符差异
        raw_search = find_query_despite_whitespace(document, target)
        if raw_search is not None:
            return raw_search

    # 第三层：模糊匹配，基于句子级别的语义相似度
    # 将文档按句子分割，使用标点符号和换行符作为分隔符
    sentences = re.split(r'[.!?]\s*|\n', document)

    # 使用模糊匹配找到与目标最相似的句子
    # token_sort_ratio: 忽略词序的相似度评分方法
    best_match = process.extractOne(target, sentences, scorer=fuzz.token_sort_ratio)

    # 设置相似度阈值为98%，确保匹配质量
    # 低于此阈值的匹配被认为是不可靠的
    if best_match[1] < 98:
        return None
    
    # 提取最佳匹配的句子
    reference = best_match[0]

    # 在原文档中定位匹配句子的位置
    start_index = document.find(reference)
    end_index = start_index + len(reference)

    return reference, start_index, end_index

def openai_token_count(string: str) -> int:
    """
    计算文本字符串中的token数量
    
    这个函数使用OpenAI的tiktoken库来精确计算文本中的token数量。
    这对于控制输入长度、估算API成本和优化分块大小非常重要。
    
    参数:
        string (str): 要计算token数量的文本字符串
        
    返回:
        int: 文本中的token数量
        
    编码器说明:
        - 使用cl100k_base编码器，这是GPT-3.5和GPT-4使用的编码器
        - 与OpenAI API的token计算方式完全一致
        - 支持多语言文本的准确token计算
        
    使用示例:
        text = "这是一个测试文本"
        token_count = openai_token_count(text)
        print(f"Token数量: {token_count}")
        
    注意:
        - 不同语言的token密度不同，中文通常比英文需要更多token
        - 特殊字符和标点符号也会占用token
        - 用于精确控制模型输入长度和成本估算
    """
    # 获取OpenAI使用的标准编码器
    encoding = tiktoken.get_encoding("cl100k_base")
    
    # 编码文本并计算token数量
    # disallowed_special=() 允许所有特殊字符，确保计算准确性
    num_tokens = len(encoding.encode(string, disallowed_special=()))
    
    return num_tokens

class Language(str, Enum):
    """
    编程语言枚举类
    
    这个枚举类定义了支持的编程语言和文档格式类型。
    主要用于代码分块器中识别不同语言的语法结构和分隔符。
    
    支持的语言类型包括：
    - 主流编程语言：Python, Java, JavaScript, C++等
    - 脚本语言：PHP, Ruby, Perl, Lua等
    - 系统语言：C, Rust, Go等
    - 文档格式：Markdown, LaTeX, HTML, RST等
    - 配置语言：Protocol Buffers等
    
    使用示例:
        language = Language.PYTHON
        if language == Language.PYTHON:
            # 使用Python特定的分块策略
            pass
    """

    # 编译型语言
    CPP = "cpp"        # C++语言
    GO = "go"          # Go语言  
    JAVA = "java"      # Java语言
    KOTLIN = "kotlin"  # Kotlin语言
    SWIFT = "swift"    # Swift语言
    RUST = "rust"      # Rust语言
    SCALA = "scala"    # Scala语言
    CSHARP = "csharp"  # C#语言
    C = "c"            # C语言
    
    # 脚本语言
    PYTHON = "python"  # Python语言
    JS = "js"          # JavaScript语言
    TS = "ts"          # TypeScript语言
    PHP = "php"        # PHP语言
    RUBY = "ruby"      # Ruby语言
    LUA = "lua"        # Lua语言
    PERL = "perl"      # Perl语言
    
    # 传统语言
    COBOL = "cobol"    # COBOL语言
    
    # 配置和数据格式
    PROTO = "proto"    # Protocol Buffers
    SOL = "sol"        # Solidity智能合约语言
    
    # 文档和标记语言
    MARKDOWN = "markdown"  # Markdown文档格式
    LATEX = "latex"        # LaTeX文档格式
    HTML = "html"          # HTML标记语言
    RST = "rst"            # reStructuredText文档格式