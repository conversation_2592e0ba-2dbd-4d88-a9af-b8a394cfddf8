"""
文本分块评估包 (Chunking Evaluation Package)

这是一个专门用于评估和比较不同文本分块策略效果的Python包。
该包为RAG（检索增强生成）系统和其他需要文本分块的应用提供了完整的解决方案。

=== 核心功能 ===

🔧 分块器实现：
   - 提供多种文本分块算法
   - 支持基于token、语义和LLM的分块策略
   - 统一的分块器接口，便于切换和比较

📊 评估框架：
   - 通用评估：基于预定义基准数据集的标准化评估
   - 合成评估：基于AI生成问题-答案对的自定义评估
   - 多维度指标：精确率、召回率、IoU等

🛠️ 工具函数：
   - 文本搜索和匹配工具
   - Token计数和嵌入函数
   - 编程语言识别支持

=== 主要组件 ===

1. BaseChunker - 分块器基类
   📋 作用：所有分块器的抽象基类，定义统一接口
   🎯 用途：
      - 为自定义分块器提供基础框架
      - 确保所有分块器具有一致的API
      - 提供通用的分块逻辑和验证

2. GeneralEvaluation - 通用评估类
   📋 作用：基于预定义基准数据集进行标准化评估
   🎯 用途：
      - 快速评估分块器性能
      - 与其他研究结果进行对比
      - 获得标准化的评估指标
   💡 特点：
      - 内置高质量基准数据集
      - 支持多种文档类型
      - 提供可重现的评估结果

3. SyntheticEvaluation - 合成评估类
   📋 作用：基于AI生成的问题-答案对进行自定义评估
   🎯 用途：
      - 针对特定领域或语料库的评估
      - 大规模评估数据生成
      - 质量控制和数据过滤
   💡 特点：
      - 自动生成问题和参考答案
      - 支持质量过滤和去重
      - 可控制的数据生成过程

=== 快速开始 ===

基本使用流程：

1. 选择或实现分块器：
   ```python
   from chunking_evaluation.chunking import RecursiveTokenChunker
   chunker = RecursiveTokenChunker(chunk_size=1000)
   ```

2. 选择评估方式：
   ```python
   # 使用通用评估
   from chunking_evaluation import GeneralEvaluation
   evaluator = GeneralEvaluation()
   results = evaluator.run(chunker)
   
   # 或使用合成评估
   from chunking_evaluation import SyntheticEvaluation
   evaluator = SyntheticEvaluation(corpora_paths=['doc1.txt'], 
                                   queries_csv_path='questions.csv')
   evaluator.generate_queries_and_excerpts()
   results = evaluator.run(chunker)
   ```

3. 分析结果：
   ```python
   print(f"精确率: {results['precision_mean']:.3f}")
   print(f"召回率: {results['recall_mean']:.3f}")
   print(f"IoU分数: {results['iou_mean']:.3f}")
   ```

=== 评估指标说明 ===

📈 主要指标：
- Precision (精确率): 检索到的相关内容占检索总内容的比例
- Recall (召回率): 检索到的相关内容占总相关内容的比例  
- IoU (交并比): 预测与真实答案的交集与并集的比值
- Precision Omega: 完整精确度，不依赖检索数量的基准指标

=== 适用场景 ===

🎯 研究应用：
- 文本分块算法研究
- RAG系统优化
- 信息检索系统评估

🏢 工业应用：
- 知识库构建
- 文档处理系统
- 智能问答系统

📚 教育用途：
- 自然语言处理教学
- 信息检索课程实验
- 算法性能分析

=== 版本信息 ===
版本: 0.1.0
作者: Brandon A. Smith
许可: MIT License
"""

# =============================================================================
# 核心组件导入
# =============================================================================

# 分块器基类 - 所有分块器的抽象基类
from .chunking.base_chunker import BaseChunker

# 评估框架 - 提供评估方式
try:
    from .evaluation_framework.general_evaluation import GeneralEvaluation  # 通用评估类
except ImportError:
    # 如果 general_evaluation 不存在，使用 base_evaluation
    from .evaluation_framework.base_evaluation import BaseEvaluation as GeneralEvaluation

try:
    from .evaluation_framework.synthetic_evaluation import SyntheticEvaluation  # 合成评估类
except ImportError:
    # 如果 synthetic_evaluation 不存在，使用 base_evaluation
    from .evaluation_framework.base_evaluation import BaseEvaluation as SyntheticEvaluation

# 工具函数 - 导入所有实用工具函数
from .utils import *

# =============================================================================
# 公共接口定义
# =============================================================================

# 定义包的公共API，这些是用户可以直接导入使用的主要类
__all__ = [
    # 核心基类
    'BaseChunker',           # 分块器基类，用于创建自定义分块器
    
    # 评估框架
    'GeneralEvaluation',     # 通用评估类，使用预定义基准数据集
    'SyntheticEvaluation',   # 合成评估类，使用AI生成的评估数据
    
    # 注意：工具函数通过 from .utils import * 导入，
    # 包括 rigorous_document_search, get_openai_embedding_function 等
]

# =============================================================================
# 包元数据
# =============================================================================

__version__ = "0.1.0"
__author__ = "Brandon A. Smith"
__email__ = "<EMAIL>"
__description__ = "一个用于评估多种文本分块方法的Python包，同时提供了新的分块方法实现"
__url__ = "https://github.com/yourusername/chunking_evaluation"

# =============================================================================
# 使用建议和最佳实践
# =============================================================================

def get_evaluation_recommendation(data_type: str = "general", scale: str = "small"):
    """
    根据数据类型和规模推荐合适的评估方式
    
    参数:
        data_type (str): 数据类型
            - "general": 通用文档（推荐GeneralEvaluation）
            - "domain": 特定领域文档（推荐SyntheticEvaluation）
            - "custom": 自定义数据集（推荐SyntheticEvaluation）
        scale (str): 评估规模
            - "small": 小规模评估（< 100个问题）
            - "medium": 中等规模评估（100-1000个问题）
            - "large": 大规模评估（> 1000个问题）
            
    返回:
        dict: 包含推荐评估方式和配置建议的字典
        
    示例:
        recommendation = get_evaluation_recommendation("domain", "medium")
        print(recommendation["evaluator"])  # "SyntheticEvaluation"
        print(recommendation["config"])     # 配置建议
    """
    recommendations = {
        ("general", "small"): {
            "evaluator": "GeneralEvaluation",
            "config": "使用默认配置即可，快速获得标准化结果",
            "pros": "快速、标准化、可重现",
            "cons": "可能不适合特定领域"
        },
        ("general", "medium"): {
            "evaluator": "GeneralEvaluation", 
            "config": "可以增加检索数量(retrieve参数)以获得更全面的评估",
            "pros": "全面的标准化评估",
            "cons": "计算时间较长"
        },
        ("general", "large"): {
            "evaluator": "GeneralEvaluation",
            "config": "建议使用持久化数据库缓存分块结果",
            "pros": "大规模标准化评估",
            "cons": "需要较多计算资源"
        },
        ("domain", "small"): {
            "evaluator": "SyntheticEvaluation",
            "config": "生成50-100个问题，使用精确模式",
            "pros": "针对性强，质量高",
            "cons": "需要OpenAI API"
        },
        ("domain", "medium"): {
            "evaluator": "SyntheticEvaluation", 
            "config": "生成200-500个问题，启用质量过滤",
            "pros": "平衡质量和数量",
            "cons": "API成本较高"
        },
        ("domain", "large"): {
            "evaluator": "SyntheticEvaluation",
            "config": "分批生成，启用去重和质量过滤",
            "pros": "大规模定制化评估",
            "cons": "成本高，时间长"
        },
        ("custom", "small"): {
            "evaluator": "SyntheticEvaluation",
            "config": "手动准备少量高质量问题-答案对",
            "pros": "完全可控的评估质量",
            "cons": "人工成本高"
        },
        ("custom", "medium"): {
            "evaluator": "SyntheticEvaluation",
            "config": "结合人工和AI生成，重点质量控制",
            "pros": "质量和效率的平衡",
            "cons": "需要人工参与"
        },
        ("custom", "large"): {
            "evaluator": "SyntheticEvaluation", 
            "config": "主要依靠AI生成，严格质量过滤",
            "pros": "可扩展的定制化评估",
            "cons": "质量控制复杂"
        }
    }
    
    key = (data_type, scale)
    return recommendations.get(key, recommendations[("general", "small")])