"""
大模型配置模块

这个模块统一管理项目中所有与大模型和嵌入模型相关的配置。

主要功能：
- 统一的OpenAI客户端配置
- 嵌入模型配置管理
- 聊天模型配置管理
- 提供便捷的客户端和函数获取接口
"""

from openai import OpenAI
import chromadb.utils.embedding_functions as embedding_functions
from typing import List, Union
from chromadb.api.types import EmbeddingFunction, Embeddings

# ==================== 基础配置常量 ====================

# 聊天模型配置
CHAT_API_KEY = "sk-ab756c213e1248aea064b2e49ad24de8"
CHAT_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
CHAT_MODEL = "qwen-max-latest"

# 嵌入模型配置
EMBEDDING_API_KEY = "sk-ozycqmzxpuvwrebozcrpuizizupfeamkomtyvcfhgpgeszyi"
EMBEDDING_BASE_URL = "https://api.siliconflow.cn/v1"
EMBEDDING_MODEL = "BAAI/bge-m3"
EMBEDDING_DIMENSIONS = 1024

# 嵌入API批处理大小限制
EMBEDDING_BATCH_SIZE = 32  # 设置为32以确保不超过API限制

# ==================== 客户端单例 ====================

CHAT_CLIENT = OpenAI(api_key=CHAT_API_KEY, base_url=CHAT_BASE_URL)
EMBEDDING_CLIENT = OpenAI(api_key=EMBEDDING_API_KEY, base_url=EMBEDDING_BASE_URL)

# ==================== 自定义嵌入函数类 ====================

class BatchLimitedEmbeddingFunction:
    """
    批处理大小限制的嵌入函数包装器

    这个类包装了ChromaDB的OpenAI嵌入函数，添加了批处理大小控制，
    确保不会超过API的批处理限制。
    """

    def __init__(self, api_key: str, api_base: str, model_name: str,
                 dimensions: int, batch_size: int = EMBEDDING_BATCH_SIZE):
        """
        初始化批处理限制嵌入函数

        参数:
            api_key: API密钥
            api_base: API基础URL
            model_name: 模型名称
            dimensions: 嵌入维度
            batch_size: 批处理大小限制
        """
        self.batch_size = batch_size
        self.base_function = embedding_functions.OpenAIEmbeddingFunction(
            api_key=api_key,
            api_base=api_base,
            model_name=model_name,
            dimensions=dimensions
        )

    def __call__(self, input: List[str]) -> Embeddings:
        """
        执行嵌入计算，自动处理批处理大小限制

        参数:
            input: 要嵌入的文本列表

        返回:
            Embeddings: 嵌入向量列表
        """
        if len(input) <= self.batch_size:
            # 如果输入大小在限制内，直接调用
            return self.base_function(input)

        # 如果输入大小超过限制，分批处理
        all_embeddings = []
        for i in range(0, len(input), self.batch_size):
            batch = input[i:i + self.batch_size]
            batch_embeddings = self.base_function(batch)
            all_embeddings.extend(batch_embeddings)

        return all_embeddings

# ==================== 客户端创建函数 ====================

def get_chat_client() -> OpenAI:
    """获取聊天模型客户端单例"""
    return CHAT_CLIENT

def get_embedding_client() -> OpenAI:
    """获取嵌入模型客户端单例"""
    return EMBEDDING_CLIENT

def get_openai_client() -> OpenAI:
    """向后兼容：返回聊天模型客户端单例"""
    return CHAT_CLIENT

def get_openai_embedding_function():
    """
    获取配置好的OpenAI嵌入函数（带批处理大小限制）

    返回:
        BatchLimitedEmbeddingFunction: 配置好的嵌入函数实例
    """
    return BatchLimitedEmbeddingFunction(
        api_key=EMBEDDING_API_KEY,
        api_base=EMBEDDING_BASE_URL,
        model_name=EMBEDDING_MODEL,
        dimensions=EMBEDDING_DIMENSIONS,
        batch_size=EMBEDDING_BATCH_SIZE
    )

# ==================== 便捷函数 ====================

def create_chat_completion(messages, model: str = CHAT_MODEL, **kwargs):
    return CHAT_CLIENT.chat.completions.create(
        model=model,
        messages=messages,
        **kwargs,
    )

def create_embeddings(input_texts, model: str = EMBEDDING_MODEL, dimensions: int = EMBEDDING_DIMENSIONS, **kwargs):
    return EMBEDDING_CLIENT.embeddings.create(
        model=model,
        dimensions=dimensions,
        input=input_texts,
        **kwargs,
    )