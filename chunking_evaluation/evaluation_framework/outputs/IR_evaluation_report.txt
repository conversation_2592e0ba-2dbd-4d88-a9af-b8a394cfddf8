中文数据集分块器与稠密检索评估报告
================================================================================

❌ 失败的配置:
--------------------------------------------------
配置: FixedTokenChunker_chunk_overlap20_chunk_size100
类型: FixedTokenChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: FixedTokenChunker_chunk_overlap50_chunk_size200
类型: FixedTokenChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: RecursiveTokenChunker_chunk_overlap20_chunk_size100
类型: RecursiveTokenChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: RecursiveTokenChunker_chunk_overlap50_chunk_size200
类型: RecursiveTokenChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: ClusterSemanticChunker_max_chunk_size100_min_chunk_size20
类型: ClusterSemanticChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: ClusterSemanticChunker_max_chunk_size200_min_chunk_size50
类型: ClusterSemanticChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: KamradtModifiedChunker_avg_chunk_size100_min_chunk_size20
类型: KamradtModifiedChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
配置: KamradtModifiedChunker_avg_chunk_size200_min_chunk_size50
类型: KamradtModifiedChunker
错误: Collection expecting embedding with dimension of 1024, got 384
------------------------------
