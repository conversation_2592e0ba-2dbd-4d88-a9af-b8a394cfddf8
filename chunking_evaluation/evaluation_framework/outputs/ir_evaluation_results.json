{"FixedTokenChunker_chunk_overlap20_chunk_size100": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "FixedTokenChunker_chunk_overlap50_chunk_size200": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "RecursiveTokenChunker_chunk_overlap20_chunk_size100": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "RecursiveTokenChunker_chunk_overlap50_chunk_size200": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "ClusterSemanticChunker_max_chunk_size100_min_chunk_size20": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "ClusterSemanticChunker_max_chunk_size200_min_chunk_size50": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "KamradtModifiedChunker_avg_chunk_size100_min_chunk_size20": {"error": "Collection expecting embedding with dimension of 1024, got 384"}, "KamradtModifiedChunker_avg_chunk_size200_min_chunk_size50": {"error": "Collection expecting embedding with dimension of 1024, got 384"}}