"""
文本分块评估框架的基础评估类模块

这个模块包含了用于评估不同文本分块策略效果的核心功能。
主要用于测量分块算法在信息检索任务中的性能表现。
"""

from .llm_config import get_openai_embedding_function
import os
import pandas as pd
import json
from retrieval import DenseRetriever

class BaseEvaluation:
    """
    文本分块评估的基础类
    
    这个类提供了评估不同文本分块策略效果的核心功能。它通过比较分块结果
    与预定义的问题-答案对来测量分块算法的性能。
    
    主要功能包括：
    - 加载和管理问题数据集
    - 创建和管理向量数据库
    - 支持不同的嵌入函数和检索策略
    - 执行检索并返回内容级调试信息
    """
    
    def __init__(self, questions_csv_path: str, corpora_id_paths=None):
        """
        初始化基础评估类
        
        参数:
            questions_csv_path (str): 问题数据集CSV文件的路径
            corpora_id_paths (dict, 可选): 语料库ID到文件路径的映射字典
        """
        # 存储语料库ID到路径的映射
        self.corpora_id_paths = corpora_id_paths

        # 存储问题数据集的CSV文件路径
        self.questions_csv_path = questions_csv_path

        # 初始化语料库列表
        self.corpus_list = []

        # 加载问题数据框
        self._load_questions_df()

        # 注释掉的代码是直接加载CSV的简单方式
        # self.questions_df = pd.read_csv(questions_csv_path)
        # self.questions_df['references'] = self.questions_df['references'].apply(json.loads)

        # 不再使用持久化向量数据库

    def _load_questions_df(self):
        """
        加载问题数据框
        
        这个私有方法负责从CSV文件加载问题数据，如果文件不存在则创建空的数据框。
        同时解析references列中的JSON数据，并提取所有唯一的语料库ID。
        """
        if os.path.exists(self.questions_csv_path):
            # 如果CSV文件存在，读取数据
            self.questions_df = pd.read_csv(self.questions_csv_path)
            # 将references列从JSON字符串转换为Python对象
            self.questions_df['references'] = self.questions_df['references'].apply(json.loads)
            # 统一标准化 corpus_id 为相对路径（相对于 datasets 根目录）
            try:
                from pathlib import Path
                datasets_dir = Path(__file__).parent / 'general_evaluation_data' / 'datasets'
                datasets_dir = datasets_dir.resolve()

                def _normalize_corpus_id(value):
                    try:
                        p = Path(str(value))
                        # 若是绝对路径，尝试转为相对 datasets 的路径
                        if p.is_absolute():
                            try:
                                return str(p.resolve().relative_to(datasets_dir))
                            except Exception:
                                pass
                        # 若字符串中包含 datasets 片段，做字符串截断
                        marker = str(datasets_dir) + os.sep
                        value_str = str(value)
                        idx = value_str.find(marker)
                        if idx != -1:
                            return value_str[idx + len(marker):]
                        # 兼容可能的不同分隔符
                        alt_marker = 'evaluation_framework' + os.sep + 'general_evaluation_data' + os.sep + 'datasets' + os.sep
                        idx2 = value_str.find(alt_marker)
                        if idx2 != -1:
                            return value_str[idx2 + len(alt_marker):]
                        return str(value)
                    except Exception:
                        return str(value)

                self.questions_df['corpus_id'] = self.questions_df['corpus_id'].apply(_normalize_corpus_id)
            except Exception:
                # 标准化失败时静默继续
                pass
        else:
            # 如果文件不存在，创建空的数据框，包含必要的列
            self.questions_df = pd.DataFrame(columns=['question', 'references', 'corpus_id'])
        
        # 提取所有唯一的语料库ID，用于后续处理
        self.corpus_list = self.questions_df['corpus_id'].unique().tolist()

    def _get_chunks_and_metadata(self, splitter):
        """
        使用指定的分块器对所有语料库进行分块，并生成元数据
        
        这个方法是评估流程的核心步骤之一。它使用提供的分块器对每个语料库
        进行文本分块，并为每个分块生成元数据。
        
        参数:
            splitter: 文本分块器对象，必须有split_text方法
            
        返回:
            tuple: (documents, metadatas)
                - documents: 所有分块文本的列表
                - metadatas: 对应的元数据列表，仅包含 corpus_id
                
        注意:
            如果分块文本重复，元数据可能不准确，因为使用.find()方法查找起始位置。
            但对于超过1000字符的分块，这通常不是问题。
        """
        documents = []  # 存储所有分块文本
        metadatas = []  # 存储所有分块的元数据
        
        # 遍历每个语料库
        for corpus_id in self.corpus_list:
            # corpus_id 现在是相对路径；若提供了映射（相对ID -> 绝对路径）优先使用
            corpus_path = None
            if self.corpora_id_paths is not None and corpus_id in self.corpora_id_paths:
                corpus_path = self.corpora_id_paths[corpus_id]
            else:
                # 兼容：直接将相对路径基于 datasets 目录解析
                from pathlib import Path
                base_dir = Path(__file__).parent / 'general_evaluation_data' / 'datasets'
                corpus_path = str((base_dir / corpus_id).resolve())
    
            # 检查操作系统并在Windows上使用UTF-8编码
            # 这可以防止读取包含非ASCII字符的文件时出现UnicodeDecodeError
            import platform
            if platform.system() == 'Windows':
                with open(corpus_path, 'r', encoding='utf-8') as file:
                    corpus = file.read()
            else:
                # 在其他系统上使用默认编码
                with open(corpus_path, 'r') as file:
                    corpus = file.read()
    
            # 使用分块器对当前语料库进行分块
            current_documents = splitter.split_text(corpus)
            current_metadatas = []

            # 为每个分块生成元数据（仅包含语料库位置）
            for _document in current_documents:
                current_metadatas.append({
                    "corpus_id": corpus_id
                })

            # 将当前语料库的分块和元数据添加到总列表中
            documents.extend(current_documents)
            metadatas.extend(current_metadatas)
            
        return documents, metadatas

    def _collect_debug_info(self, retrievals, highlighted_chunks_count):
        """
        收集详细的调试信息

        参数:
            retrievals: 检索结果，包含documents、metadatas、distances
            highlighted_chunks_count: 每个问题采用的检索结果数量（top-k）

        返回:
            dict: 包含每个问题详细信息的字典
        """
        debug_info = {
            "questions": [],
            "total_questions": len(self.questions_df)
        }

        # 遍历每个问题，收集详细信息
        for index, row in self.questions_df.iterrows():
            question_text = row['question']
            references = row['references']
            corpus_id = row['corpus_id']

            # 获取该问题的检索结果
            question_retrievals = {
                'documents': retrievals['documents'][index] if index < len(retrievals['documents']) else [],
                'metadatas': retrievals['metadatas'][index] if index < len(retrievals['metadatas']) else [],
                'distances': retrievals['distances'][index] if index < len(retrievals['distances']) else []
            }

            # 构建检索结果详情
            retrieval_results = []
            chunk_count = highlighted_chunks_count[index] if index < len(highlighted_chunks_count) else 0

            for i in range(min(len(question_retrievals['documents']), chunk_count)):
                retrieval_results.append({
                    "rank": i + 1,
                    "document_content": question_retrievals['documents'][i],
                    "similarity_score": 1.0 - question_retrievals['distances'][i],  # 转换距离为相似度
                    "distance": question_retrievals['distances'][i],
                    "metadata": question_retrievals['metadatas'][i]
                })

            # 构建标准答案信息
            ground_truth = []
            for ref in references:
                # 仅保留内容与语料ID
                content_value = ref['content'] if isinstance(ref, dict) else str(ref)
                ground_truth.append({
                    "content": content_value,
                    "corpus_id": corpus_id
                })

            # 构建评估指标详情
            metrics_detail = {
                "chunks_retrieved": chunk_count
            }

            # 添加问题的完整调试信息
            question_debug = {
                "question_index": index,
                "question_text": question_text,
                "corpus_id": corpus_id,
                "ground_truth": ground_truth,
                "retrieval_results": retrieval_results,
                "metrics": metrics_detail
            }

            debug_info["questions"].append(question_debug)

        return debug_info

    # 持久化相关逻辑已移除
    
    def _convert_question_references_to_json(self):
        """
        将问题数据框中的references列从JSON字符串转换为Python对象
        
        这个私有方法用于安全地解析references列中的JSON数据。
        如果解析失败，会静默跳过而不抛出异常。
        """
        def safe_json_loads(row):
            """
            安全的JSON解析函数
            
            参数:
                row: 要解析的JSON字符串
                
            返回:
                解析后的Python对象，如果解析失败则返回None
            """
            try:
                return json.loads(row)
            except:
                pass  # 解析失败时静默跳过

        # 对references列应用安全的JSON解析
        self.questions_df['references'] = self.questions_df['references'].apply(safe_json_loads)


    def run(self, chunker, embedding_function=None, retrieve:int = 5, include_debug_info: bool = False, retriever=None):
        """
        运行分块器评估的主要方法（内容级检索 + 调试信息）

        参数:
            chunker: 要评估的文本分块器对象，必须有split_text方法
            embedding_function: 用于计算向量相似度的嵌入函数。如果未提供，将使用默认的OpenAI嵌入函数
                               注意：如果提供了retriever参数，此参数可能被忽略
            retrieve (int): 每个问题要检索的分块数量。如果设置为-1，使用默认 top-5。
            include_debug_info (bool): 是否包含详细的调试信息，包括每个问题的检索结果和评估计算过程
            retriever (BaseRetriever, 可选): 自定义检索器。如果提供，将使用此检索器而不是默认的稠密检索。
                                           支持稠密检索器、稀疏检索器和混合检索器

        返回:
            dict: 仅包含：
                - debug_info (可选): 每个问题的调试信息（检索结果与参考内容）
                - retriever_info (可选): 检索器的详细信息
        """
        # 重新加载问题数据框，确保使用最新数据
        self._load_questions_df()
        
        # 如果没有提供嵌入函数，使用默认的OpenAI嵌入函数
        if embedding_function is None:
            embedding_function = get_openai_embedding_function()
        # 生成分块文档与元数据（纯内存）
        documents, metadatas = self._get_chunks_and_metadata(chunker)

        # 将问题数据框按索引升序排序
        self.questions_df = self.questions_df.sort_index()

        # 确定检索的分块数量。
        # 若 retrieve == -1，采用固定默认值（top-5）。
        if retrieve == -1:
            maximum_n = 5
            highlighted_chunks_count = [maximum_n] * len(self.questions_df)
        else:
            maximum_n = retrieve
            highlighted_chunks_count = [retrieve] * len(self.questions_df)

        # 执行检索：支持自定义检索器或默认的 DenseRetriever（纯内存）
        if retriever is None:
            retriever = DenseRetriever(
                embedding_function=embedding_function,
                collection_name="default_dense_retrieval"
            )
        
        retrievals = self._retrieve_with_custom_retriever(
            retriever=retriever,
            maximum_n=maximum_n,
            documents=documents,
            metadatas=metadatas
        )

        # 仅收集调试信息
        debug_info = None
        if include_debug_info:
            debug_info = self._collect_debug_info(retrievals, highlighted_chunks_count)


        # 构建返回结果（仅包含调试信息与可选检索器信息）
        result = {}

        # 如果包含调试信息，添加到结果中
        if include_debug_info and debug_info is not None:
            result["debug_info"] = debug_info

        # 如果使用了自定义检索器，添加检索器信息
        if retriever is not None:
            result["retriever_info"] = retriever.get_info()

        return result

    def _retrieve_with_custom_retriever(self, retriever, maximum_n, documents, metadatas):
        """
        使用自定义检索器进行检索

        这个方法将自定义检索器与现有的评估框架集成，确保检索结果
        与原有的ChromaDB格式兼容。

        参数:
            retriever: 自定义检索器实例
            maximum_n: 最大检索结果数量
            documents: 分块后的文档列表
            metadatas: 对应的元数据列表

        返回:
            dict: 与ChromaDB query方法兼容的检索结果格式
        """
        # 构建自定义检索器的索引
        if not retriever.is_built():
            retriever.build_index(documents, metadatas)

        # 获取问题文本
        questions = self.questions_df['question'].tolist()

        # 使用自定义检索器进行检索
        retrieval_results = retriever.retrieve(questions, maximum_n)

        # 确保结果是列表格式
        if not isinstance(retrieval_results, list):
            retrieval_results = [retrieval_results]

        # 转换为ChromaDB兼容格式
        retrievals = {
            'documents': [],
            'metadatas': [],
            'distances': []
        }

        for result in retrieval_results:
            retrievals['documents'].append(result.documents)
            retrievals['metadatas'].append(result.metadatas)
            retrievals['distances'].append(result.distances)

        return retrievals
