#!/usr/bin/env python3
"""
中文数据集分块与稠密检索评估脚本

基于信息检索指标进行分块策略评估
"""

import os
import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from metrics.evaluation_metrics import ComprehensiveEvaluator

from chunking_evaluation.evaluation_framework.base_evaluation import BaseEvaluation
from chunking_evaluation.chunking import (
    FixedTokenChunker,
    RecursiveTokenChunker,
    ClusterSemanticChunker,  
    KamradtModifiedChunker   
)
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function

# 导入检索器
from retrieval import DenseRetriever


class ChineseEvaluation(BaseEvaluation):
    """
    中文数据集评估类

    专门用于评估分块策略在中文数据集上的表现。
    继承自BaseEvaluation，使用中文问题-答案对进行评估。
    """

    def __init__(self, questions_csv_path: str):
        """
        初始化中文评估类

        参数:
            questions_csv_path (str): 中文问题数据集CSV文件路径
            chroma_db_path (str, 可选): ChromaDB数据库路径
        """
        # 构建语料库路径映射
        corpora_id_paths = self._build_corpora_paths()

        # 调用父类构造函数
        super().__init__(questions_csv_path, corpora_id_paths=corpora_id_paths)

    def _build_corpora_paths(self) -> Dict[str, str]:
        """
        构建语料库ID到文件路径的映射

        返回:
            Dict[str, str]: 语料库ID到文件路径的映射字典
        """
        corpora_paths = {}

        # 获取当前脚本所在目录
        current_dir = Path(__file__).parent

        # datasets目录路径
        datasets_dir = current_dir / "evaluation_framework" / "general_evaluation_data" / "datasets"

        # 检查datasets目录是否存在
        if not datasets_dir.exists():
            print(f"警告：datasets目录不存在: {datasets_dir}")
            return corpora_paths

        # 遍历datasets目录下的所有子目录
        for subdir in datasets_dir.iterdir():
            if subdir.is_dir():
                # 遍历该子目录下的所有.txt文件
                for file in subdir.iterdir():
                    if file.suffix == '.txt':
                        # 使用相对路径作为corpus_id（相对于datasets根目录），提高可移植性
                        try:
                            relative_path = str(file.relative_to(datasets_dir))
                        except Exception:
                            # 理论上不会发生（file在datasets_dir之下），兜底使用文件名
                            relative_path = file.name
                        corpora_paths[relative_path] = str(file.absolute())

        return corpora_paths

def get_chunker_configs() -> Dict[str, List[Dict[str, Any]]]:
    """
    获取所有分块器的参数配置列表

    返回:
        Dict[str, List[Dict[str, Any]]]: 分块器名称到参数配置列表的映射
    """
    configs = {}

    # 1. 固定token分块器的参数配置
    configs['FixedTokenChunker'] = [
        {'chunk_size': 100, 'chunk_overlap': 20},
        {'chunk_size': 200, 'chunk_overlap': 50},
    ]

    # 2. 递归token分块器的参数配置
    configs['RecursiveTokenChunker'] = [
        {'chunk_size': 100, 'chunk_overlap': 20},
        {'chunk_size': 200, 'chunk_overlap': 50},
    ]

    # 3. 聚类语义分块器的参数配置
    configs['ClusterSemanticChunker'] = [
        {'max_chunk_size': 100, 'min_chunk_size': 20},
        {'max_chunk_size': 200, 'min_chunk_size': 50},
    ]

    # 4. Kamradt改进版分块器的参数配置 
    configs['KamradtModifiedChunker'] = [
        {'avg_chunk_size': 100, 'min_chunk_size': 20},
        {'avg_chunk_size': 200, 'min_chunk_size': 50},
    ]

    return configs

def generate_db_name(chunker_type: str, params: Dict[str, Any]) -> str:
    """
    根据分块器类型和参数生成数据库名称

    参数:
        chunker_type: 分块器类型
        params: 参数字典

    返回:
        str: 格式化的数据库名称
    """
    # 将参数转换为字符串，按键排序确保一致性
    param_parts = []
    for key, value in sorted(params.items()):
        param_parts.append(f"{key}{value}")

    param_str = "_".join(param_parts)
    return f"{chunker_type}_{param_str}"

def check_db_exists(base_db_path: str, db_name: str) -> bool:
    """
    检查指定名称的数据库是否已存在

    参数:
        base_db_path: 数据库基础路径
        db_name: 数据库名称

    返回:
        bool: 数据库是否存在
    """
    db_path = Path(base_db_path) / db_name
    return db_path.exists() and any(db_path.iterdir())

def create_chunkers_with_configs() -> Dict[str, Any]:
    """
    创建所有分块器配置的实例

    返回:
        Dict[str, Any]: 分块器配置名称到分块器实例的映射
    """
    chunkers = {}
    configs = get_chunker_configs()

    # 创建embedding函数（用于语义分块器）
    try:
        embedding_function = get_openai_embedding_function()
        embedding_available = True
    except Exception as e:
        print(f"警告：无法创建embedding函数: {e}")
        embedding_available = False

    # 为每种分块器创建不同参数配置的实例
    for chunker_type, param_configs in configs.items():
        for i, params in enumerate(param_configs):
            config_name = f"{chunker_type}_config_{i+1}"

            try:
                if chunker_type == 'FixedTokenChunker':
                    chunkers[config_name] = {
                        'chunker': FixedTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }

                elif chunker_type == 'RecursiveTokenChunker':
                    chunkers[config_name] = {
                        'chunker': RecursiveTokenChunker(**params),
                        'params': params,
                        'type': chunker_type
                    }

                elif chunker_type == 'ClusterSemanticChunker' and embedding_available:
                    chunkers[config_name] = {
                        'chunker': ClusterSemanticChunker(
                            embedding_function=embedding_function,
                            **params
                        ),
                        'params': params,
                        'type': chunker_type
                    }

                elif chunker_type == 'KamradtModifiedChunker' and embedding_available:
                    chunkers[config_name] = {
                        'chunker': KamradtModifiedChunker(
                            embedding_function=embedding_function,
                            **params
                        ),
                        'params': params,
                        'type': chunker_type
                    }

            except Exception as e:
                print(f"警告：无法创建 {config_name}: {e}")

    return chunkers

def run_evaluation(questions_csv_path: str):
    """
    运行中文数据集的分块器与稠密检索评估

    参数:
        questions_csv_path (str): 中文问题数据集CSV文件路径
        base_chroma_db_path (str): ChromaDB数据库基础路径

    返回:
        Dict[str, Any]: 评估结果字典
    """
    # 创建embedding函数
    try:
        embedding_function = get_openai_embedding_function()
    except Exception as e:
        print(f"错误：无法创建embedding函数: {e}")
        print("请确保设置了正确的OpenAI API密钥")
        return {}

    # 创建所有分块器配置
    chunkers = create_chunkers_with_configs()

    print(f"📊 总共将评估 {len(chunkers)} 种分块器配置（使用稠密检索）")

    # 存储评估结果
    results = {}

    # 测试每个分块器配置（使用稠密检索）
    current_config = 0

    for config_name, chunker_info in chunkers.items():
        chunker = chunker_info['chunker']
        chunker_params = chunker_info['params']
        chunker_type = chunker_info['type']
        current_config += 1

        # 生成配置名称（用于输出标识）
        chunker_db_name = generate_db_name(chunker_type, chunker_params)

        print(f"\n📊 正在评估配置 {current_config}/{len(chunkers)}: {chunker_db_name}")

        try:
            # 创建评估器
            evaluator = ChineseEvaluation(questions_csv_path)

            # 创建稠密检索器实例
            dense_retriever = DenseRetriever(
                embedding_function=embedding_function,
                collection_name='dense_retrieval'
            )

            # 运行评估（只进行检索，不计算原始位置匹配指标）
            result = evaluator.run(
                chunker=chunker,
                embedding_function=embedding_function,
                retrieve=3,
                include_debug_info=True,
                retriever=dense_retriever
            )

            # 计算信息检索指标（仅字符 n-gram 汇总为 Precision/Recall/F1）
            ir_evaluator = ComprehensiveEvaluator()
            ir_metrics = calculate_ir_metrics(
                result, evaluator.questions_df, ir_evaluator
            )

            # 存储结果
            results[chunker_db_name] = {
                'chunker_type': chunker_type,
                'chunker_params': chunker_params,
                'retriever_name': 'DenseRetriever',
                'retriever_description': '稠密检索 (Dense Retrieval) - 基于语义向量相似度',
                'config_name': chunker_db_name,
                'db_path': None,
                'db_reused': False,
                'ir_metrics': ir_metrics,
                'debug_info': result.get('debug_info', {})
            }

            print(f"✅ {chunker_db_name} 评估完成")
            print(
                f"Precision: {ir_metrics.get('metrics', {}).get('precision', {}).get('mean', 0):.3f} | "
                f"Recall: {ir_metrics.get('metrics', {}).get('recall', {}).get('mean', 0):.3f} | "
                f"F1: {ir_metrics.get('metrics', {}).get('f1', {}).get('mean', 0):.3f}"
            )

        except Exception as e:
            print(f"❌ {chunker_db_name} 评估失败: {e}")
            results[chunker_db_name] = {
                'error': str(e),
                'chunker_type': chunker_type,
                'chunker_params': chunker_params,
                'config_name': chunker_db_name
            }

    return results

def calculate_ir_metrics(original_results: Dict[str, Any], questions_df: pd.DataFrame,
                        ir_evaluator: ComprehensiveEvaluator) -> Dict[str, Any]:
    """
    基于原始评估结果计算信息检索指标

    参数:
        original_results: 原始评估结果
        questions_df: 问题数据框
        ir_evaluator: 信息检索评估器

    返回:
        Dict[str, Any]: 信息检索评估指标，包含每个问题的详细指标
    """
    debug_info = original_results.get('debug_info', {})
    questions = debug_info.get('questions', [])

    if not questions:
        return {
            'metrics': {'precision': {'mean': 0}, 'recall': {'mean': 0}, 'f1': {'mean': 0}},
            'question_metrics': []
        }

    all_evaluations = []
    question_metrics = []

    # 对每个问题计算信息检索指标
    for i, question_data in enumerate(questions):
        try:
            # 获取问题对应的参考答案
            if i < len(questions_df):
                question_row = questions_df.iloc[i]
                references = question_row['references']

                # 解析references
                if isinstance(references, str):
                    references = json.loads(references)

                # 提取检索到的分块内容
                retrieved_chunks = []
                for result in question_data.get('retrieval_results', []):
                    retrieved_chunks.append(result.get('document_content', ''))

                # 提取参考答案内容
                reference_contents = [ref.get('content', '') for ref in references]
                combined_reference = " ".join(reference_contents)

                # 执行信息检索评估
                evaluation_result = ir_evaluator.evaluate(
                    retrieved_chunks=retrieved_chunks,
                    reference_content=combined_reference
                )

                all_evaluations.append(evaluation_result)

                # 保存每个问题的指标（仅字符 n-gram 指标）
                question_metrics.append({
                    'question_index': i,
                    'precision': evaluation_result.get('precision', 0.0),
                    'recall': evaluation_result.get('recall', 0.0),
                    'f1': evaluation_result.get('f1', 0.0)
                })

        except Exception:
            # 如果计算失败，添加默认值
            question_metrics.append({
                'question_index': i,
                'precision': 0.0,
                'recall': 0.0,
                'f1': 0.0
            })
            continue

    if not all_evaluations:
        return {
            'metrics': {'precision': 0, 'recall': 0, 'f1': 0},
            'question_metrics': question_metrics
        }

    # 计算平均指标
    import numpy as np
    # 计算统计信息（仅字符 n-gram 指标）
    metrics_stats = {}
    for metric in ['precision', 'recall', 'f1']:
        values = [eval_result.get(metric, 0) for eval_result in all_evaluations]
        metrics_stats[metric] = {
            'mean': float(np.mean(values)),
            'std': float(np.std(values)),
            'min': float(np.min(values)),
            'max': float(np.max(values)),
        }

    return {
        'metrics': metrics_stats,
        'question_metrics': question_metrics,
        'sample_size': len(all_evaluations)
    }

def generate_report(results: Dict[str, Any], questions_df: pd.DataFrame = None):
    """
    生成评估报告

    参数:
        results (Dict[str, Any]): 评估结果字典
        questions_df (pd.DataFrame): 问题数据框，用于提取参考答案内容
    """
    # 创建输出目录
    output_dir = Path("chunking_evaluation/evaluation_framework/outputs")
    output_dir.mkdir(parents=True, exist_ok=True)

    # 处理并简化结果数据
    simplified_results = {}
    for config_name, result in results.items():
        if "error" not in result and "debug_info" in result:
            debug_info = result["debug_info"]
            questions = debug_info.get("questions", [])

            # 获取该配置的信息检索指标
            ir_metrics = result.get('ir_metrics', {})
            question_metrics = ir_metrics.get('question_metrics', [])

            # 处理每个问题的数据
            processed_questions = []
            for i, question in enumerate(questions):
                # 从questions_df获取参考答案内容
                ground_truth_contents = []
                if questions_df is not None:
                    question_index = question.get("question_index", 0)
                    if question_index < len(questions_df):
                        try:
                            references = questions_df.iloc[question_index]['references']
                            if isinstance(references, str):
                                references = json.loads(references)
                            ground_truth_contents = [ref.get('content', '') for ref in references]
                        except:
                            ground_truth_contents = []

                # 获取对应问题的传统信息检索指标
                question_ir_metrics = {'precision': 0.0, 'recall': 0.0, 'f1': 0.0}
                if i < len(question_metrics):
                    question_ir_metrics = {
                        'precision': question_metrics[i].get('precision', 0.0),
                        'recall': question_metrics[i].get('recall', 0.0),
                        'f1': question_metrics[i].get('f1', 0.0)
                    }

                processed_question = {
                    "question_index": question.get("question_index"),
                    "question_text": question.get("question_text"),
                    "corpus_id": question.get("corpus_id"),
                    "ground_truth": ground_truth_contents,
                    "retrieval_results": [
                        {
                            "rank": rr.get("rank"),
                            "document_content": rr.get("document_content"),
                            "similarity_score": rr.get("similarity_score"),
                            "distance": rr.get("distance")
                        }
                        for rr in question.get("retrieval_results", [])
                    ],
                    "metrics": question_ir_metrics
                }
                processed_questions.append(processed_question)

            simplified_results[config_name] = {
                "questions": processed_questions
            }
        elif "error" in result:
            # 保留错误信息以便调试
            simplified_results[config_name] = {
                "error": result["error"]
            }

    # 保存简化后的结果
    output_file = output_dir / "ir_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(simplified_results, f, ensure_ascii=False, indent=2)

    # 保存详细报告
    report_file = output_dir / "IR_evaluation_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("中文数据集分块器与稠密检索评估报告\n")
        f.write("="*80 + "\n")

        # 按分块器类型分组写入报告
        successful_results = {k: v for k, v in results.items() if "error" not in v}
        failed_results = {k: v for k, v in results.items() if "error" in v}

        # 写入成功的结果
        if successful_results:

            # 按分块器类型分组
            chunker_groups = {}
            for config_name, result in successful_results.items():
                chunker_type = result.get('chunker_type', 'Unknown')
                if chunker_type not in chunker_groups:
                    chunker_groups[chunker_type] = []
                chunker_groups[chunker_type].append((config_name, result))

            # 为每种分块器类型写入详细信息
            for chunker_type, configs in chunker_groups.items():
                f.write(f"\n{chunker_type}:\n")

                for config_name, result in configs:
                    ir_metrics = result.get('ir_metrics', {})

                    f.write(f"  配置: {config_name}\n")
                    f.write(f"  评估指标:\n")
                    f.write(f"    精确率: {ir_metrics.get('metrics', {}).get('precision', {}).get('mean', 0):.3f}\n")
                    f.write(f"    召回率: {ir_metrics.get('metrics', {}).get('recall', {}).get('mean', 0):.3f}\n")
                    f.write(f"    F1分数: {ir_metrics.get('metrics', {}).get('f1', {}).get('mean', 0):.3f}\n")
                    f.write("  " + "-"*50 + "\n")

        # 写入失败的结果
        if failed_results:
            f.write("\n❌ 失败的配置:\n")
            f.write("-"*50 + "\n")
            for config_name, result in failed_results.items():
                chunker_type = result.get('chunker_type', 'Unknown')

                f.write(f"配置: {config_name}\n")
                f.write(f"类型: {chunker_type}\n")
                f.write(f"错误: {result['error']}\n")
                f.write("-"*30 + "\n")

def main():
    print("🎯 开始信息检索评估")
    print("="*50)

    # 数据集路径配置
    questions_csv_path = "chunking_evaluation/evaluation_framework/general_evaluation_data/questions.csv"

    # 检查数据集是否存在
    if not os.path.exists(questions_csv_path):
        print(f"❌ 错误：找不到中文数据集文件: {questions_csv_path}")
        print("请确保已经创建了中文数据集")
        return

    # 加载问题数据框
    import pandas as pd
    questions_df = pd.read_csv(questions_csv_path)
    questions_df['references'] = questions_df['references'].apply(json.loads)

    # 运行评估（纯内存检索）
    results = run_evaluation(questions_csv_path)

    if results:
        # 生成评估报告
        generate_report(results, questions_df)

        print("\n🎉 评估完成！")
        print("\n📁 详细结果已保存到 evaluation_framework/outputs/ 目录")
        print("   - ir_evaluation_results.json: 完整的评估数据")
        print("   - IR_evaluation_report.txt: 详细的分析报告")
    else:
        print("\n❌ 评估过程中出现错误，请检查配置和网络连接。")

if __name__ == "__main__":
    main()
