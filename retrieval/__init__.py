"""
混合检索模块 (Hybrid Retrieval Module)

这个模块提供了完整的混合检索解决方案，支持稠密检索、稀疏检索和混合检索策略。
主要用于增强RAG（检索增强生成）系统和文本分块评估框架的检索能力。

=== 核心功能 ===

🔧 检索器实现：
   - BaseRetriever: 所有检索器的抽象基类
   - DenseRetriever: 基于向量相似度的稠密检索
   - SparseRetriever: 基于BM25的稀疏检索  
   - HybridRetriever: 组合稠密和稀疏检索的混合检索

📊 检索策略：
   - 加权线性组合：可配置的权重分配
   - RRF (Reciprocal Rank Fusion)：基于排名的融合算法
   - 两阶段检索：先筛选后精排的策略

🛠️ 特性支持：
   - 批量查询处理
   - 标准化评分机制
   - 灵活的参数配置
   - 与现有评估框架完全兼容

=== 主要组件 ===

1. BaseRetriever - 检索器基类
   📋 作用：所有检索器的抽象基类，定义统一接口
   🎯 用途：
      - 为自定义检索器提供基础框架
      - 确保所有检索器具有一致的API
      - 提供通用的检索逻辑和验证

2. DenseRetriever - 稠密检索器
   📋 作用：基于向量相似度的语义检索
   🎯 用途：
      - 语义相似性检索
      - 处理同义词和语义变体
      - 跨语言检索支持
   💡 特点：
      - 封装ChromaDB稠密检索逻辑
      - 支持多种嵌入模型
      - 高效的向量相似度计算

3. SparseRetriever - 稀疏检索器
   📋 作用：基于BM25算法的关键词检索
   🎯 用途：
      - 精确关键词匹配
      - 处理专业术语和实体名称
      - 补充稠密检索的不足
   💡 特点：
      - 高效的倒排索引
      - 可配置的BM25参数
      - 支持中英文文本

4. HybridRetriever - 混合检索器
   📋 作用：组合稠密和稀疏检索的优势
   🎯 用途：
      - 平衡语义理解和精确匹配
      - 提高检索的准确性和召回率
      - 适应不同类型的查询
   💡 特点：
      - 多种组合策略
      - 自适应权重调整
      - 性能优化的批量处理

=== 快速开始 ===

基本使用流程：

1. 稠密检索：
   ```python
   from retrieval import DenseRetriever
   retriever = DenseRetriever(embedding_function=embedding_func)
   results = retriever.retrieve(queries=["查询文本"], n_results=5)
   ```

2. 稀疏检索：
   ```python
   from retrieval import SparseRetriever
   retriever = SparseRetriever()
   retriever.build_index(documents)
   results = retriever.retrieve(queries=["查询文本"], n_results=5)
   ```

3. 混合检索：
   ```python
   from retrieval import HybridRetriever
   retriever = HybridRetriever(
       dense_weight=0.7,
       sparse_weight=0.3,
       combination_method="weighted"
   )
   results = retriever.retrieve(queries=["查询文本"], n_results=5)
   ```

=== 与评估框架集成 ===

```python
from chunking_evaluation import BaseEvaluation
from retrieval import HybridRetriever

# 创建混合检索器
retriever = HybridRetriever()

# 在评估中使用
evaluator = BaseEvaluation()
results = evaluator.run(chunker, retriever=retriever)
```

=== 选择指南 ===

🚀 性能优先：
   - 最快速度：SparseRetriever (BM25)
   - 平衡速度与质量：DenseRetriever

🎯 质量优先：
   - 最高准确性：HybridRetriever
   - 语义理解：DenseRetriever
   - 精确匹配：SparseRetriever

📊 应用场景：
   - 通用文档检索：HybridRetriever
   - 代码搜索：SparseRetriever + DenseRetriever
   - 学术论文检索：HybridRetriever (高稠密权重)
   - 法律文档检索：HybridRetriever (高稀疏权重)
"""

from .base_retriever import BaseRetriever
from .dense_retriever import DenseRetriever
from .sparse_retriever import SparseRetriever
from .hybrid_retriever import HybridRetriever

__all__ = [
    'BaseRetriever',
    'DenseRetriever', 
    'SparseRetriever',
    'HybridRetriever'
]

# 版本信息
__version__ = "1.0.0"
__author__ = "Chunking Evaluation Team"
__description__ = "Hybrid retrieval module for enhanced text retrieval capabilities"

def get_recommended_retriever(use_case: str = "general"):
    """
    根据使用场景推荐合适的检索器
    
    参数:
        use_case (str): 使用场景，可选值：
            - "speed": 优先考虑检索速度
            - "quality": 优先考虑检索质量  
            - "general": 平衡速度和质量（默认）
            - "semantic": 需要语义理解
            - "keyword": 需要精确关键词匹配
            - "hybrid": 需要混合检索能力
            
    返回:
        str: 推荐的检索器类名
        
    示例:
        recommended = get_recommended_retriever("quality")
        print(f"推荐使用: {recommended}")
    """
    recommendations = {
        "speed": "SparseRetriever",
        "quality": "HybridRetriever", 
        "general": "HybridRetriever",
        "semantic": "DenseRetriever",
        "keyword": "SparseRetriever",
        "hybrid": "HybridRetriever"
    }
    
    return recommendations.get(use_case, "HybridRetriever")
