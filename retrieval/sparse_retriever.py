"""
稀疏检索器模块

这个模块实现了基于BM25算法的稀疏检索器，提供精确的关键词匹配能力。
BM25是一种经典的信息检索算法，特别适合处理专业术语和实体名称的精确匹配。
"""

import pickle
import os
from typing import List, Dict, Any, Union, Optional
import numpy as np

try:
    from rank_bm25 import BM25Okapi
except ImportError:
    raise ImportError("rank_bm25 is required for SparseRetriever. Install it with: pip install rank_bm25")

from .base_retriever import BaseRetriever, RetrievalResult


class SparseRetriever(BaseRetriever):
    """
    稀疏检索器
    
    基于BM25算法的关键词检索器，提供精确的词汇匹配能力。
    特别适合处理专业术语、实体名称和需要精确匹配的场景。
    
    特点：
    - 精确关键词匹配
    - 高效的倒排索引
    - 支持中英文文本
    - 可配置的BM25参数
    - 快速检索速度
    """
    
    def __init__(self, 
                 k1: float = 1.5,
                 b: float = 0.75,
                 tokenizer: Optional[callable] = None,
                 name: str = None):
        """
        初始化稀疏检索器
        
        参数:
            k1: BM25参数，控制词频饱和度，通常在1.2-2.0之间
            b: BM25参数，控制文档长度归一化，通常在0.5-0.8之间
            tokenizer: 分词器函数，如果为None则使用默认的简单分词
            name: 检索器名称
        """
        super().__init__(name or "SparseRetriever")
        
        self.k1 = k1
        self.b = b
        self.tokenizer = tokenizer or self._default_tokenizer
        
        # BM25相关属性
        self.bm25 = None
        self.documents = []
        self.metadatas = []
        self.tokenized_docs = []
        
    def _default_tokenizer(self, text: str) -> List[str]:
        """
        默认分词器
        
        提供简单但有效的分词功能，支持中英文文本。
        
        参数:
            text: 输入文本
            
        返回:
            List[str]: 分词结果
        """
        import re
        
        # 转换为小写
        text = text.lower()
        
        # 使用正则表达式分词，保留中文字符、英文单词和数字
        tokens = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+', text)
        
        # 对中文进行字符级分词
        result = []
        for token in tokens:
            if re.match(r'[\u4e00-\u9fff]+', token):
                # 中文字符级分词
                result.extend(list(token))
            else:
                # 英文和数字保持原样
                result.append(token)
        
        return result
    
    def build_index(self, 
                   documents: List[str], 
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建稀疏检索索引
        
        使用BM25算法构建倒排索引，支持高效的关键词检索。
        
        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表
            
        异常:
            ValueError: 如果documents和metadatas长度不匹配
        """
        if len(documents) != len(metadatas):
            raise ValueError("documents and metadatas must have the same length")
        
        self.documents = documents.copy()
        self.metadatas = metadatas.copy()
        
        # 对所有文档进行分词
        self.tokenized_docs = [self.tokenizer(doc) for doc in documents]

        # 构建BM25索引
        self.bm25 = BM25Okapi(self.tokenized_docs, k1=self.k1, b=self.b)

        self._is_built = True
    
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行稀疏检索
        
        使用BM25算法检索最相关的文档。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        # 标准化查询输入
        query_list = self._normalize_queries(queries)
        is_single_query = isinstance(queries, str)
        
        retrieval_results = []
        
        for query in query_list:
            # 对查询进行分词
            tokenized_query = self.tokenizer(query)
            
            # 使用BM25计算分数
            scores = self.bm25.get_scores(tokenized_query)
            
            # 获取top-k结果的索引
            top_indices = np.argsort(scores)[::-1][:n_results]
            
            # 构建结果
            result_docs = []
            result_metas = []
            result_scores = []
            result_distances = []
            
            for idx in top_indices:
                if idx < len(self.documents):
                    result_docs.append(self.documents[idx])
                    result_metas.append(self.metadatas[idx])
                    score = scores[idx]
                    result_scores.append(score)
                    # 将分数转换为距离（分数越高，距离越小）
                    # 使用 1/(1+score) 确保距离在[0,1]范围内
                    distance = 1.0 / (1.0 + score) if score > 0 else 1.0
                    result_distances.append(distance)
            
            # 创建RetrievalResult对象
            retrieval_result = RetrievalResult(
                documents=result_docs,
                metadatas=result_metas,
                distances=result_distances,
                scores=result_scores
            )
            retrieval_results.append(retrieval_result)
        
        # 根据输入类型返回相应格式
        if is_single_query:
            return retrieval_results[0]
        return retrieval_results
    
    def get_bm25_info(self) -> Dict[str, Any]:
        """
        获取BM25索引信息
        
        返回:
            Dict[str, Any]: 包含BM25索引详细信息的字典
        """
        if not self._is_built:
            return {"error": "Index not built"}
        
        return {
            "document_count": len(self.documents),
            "vocabulary_size": len(self.bm25.idf) if self.bm25 else 0,
            "k1": self.k1,
            "b": self.b,
            "tokenizer": self.tokenizer.__name__ if hasattr(self.tokenizer, '__name__') else "custom"
        }
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取检索器详细信息
        
        返回:
            Dict[str, Any]: 包含检索器和BM25信息的字典
        """
        base_info = super().get_info()
        bm25_info = self.get_bm25_info()
        
        return {
            **base_info,
            "bm25_info": bm25_info
        }
    
    def save_index(self, path: str) -> None:
        """
        保存索引到指定路径
        
        参数:
            path: 保存路径（文件路径，不是目录）
            
        异常:
            RuntimeError: 如果索引未构建
        """
        if not self._is_built:
            raise RuntimeError("Cannot save index that hasn't been built")
        
        # 准备要保存的数据
        index_data = {
            'bm25': self.bm25,
            'documents': self.documents,
            'metadatas': self.metadatas,
            'tokenized_docs': self.tokenized_docs,
            'k1': self.k1,
            'b': self.b,
            'name': self.name
        }
        
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # 保存到pickle文件
        with open(path, 'wb') as f:
            pickle.dump(index_data, f)
    
    @classmethod
    def load_index(cls, 
                   path: str, 
                   tokenizer: Optional[callable] = None) -> 'SparseRetriever':
        """
        从指定路径加载索引
        
        参数:
            path: 索引文件路径
            tokenizer: 分词器函数，如果为None则使用保存时的分词器
            
        返回:
            SparseRetriever: 加载的检索器实例
            
        异常:
            FileNotFoundError: 如果索引文件不存在
            ValueError: 如果索引文件格式错误
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"Index file not found: {path}")
        
        try:
            # 加载索引数据
            with open(path, 'rb') as f:
                index_data = pickle.load(f)
            
            # 创建检索器实例
            retriever = cls(
                k1=index_data.get('k1', 1.5),
                b=index_data.get('b', 0.75),
                tokenizer=tokenizer,  # 使用提供的分词器或默认分词器
                name=index_data.get('name', 'SparseRetriever')
            )
            
            # 恢复索引数据
            retriever.bm25 = index_data['bm25']
            retriever.documents = index_data['documents']
            retriever.metadatas = index_data['metadatas']
            retriever.tokenized_docs = index_data['tokenized_docs']
            retriever._is_built = True

            return retriever
            
        except Exception as e:
            raise ValueError(f"Failed to load index from {path}: {e}")
    
    def get_document_scores(self, query: str) -> List[float]:
        """
        获取查询对所有文档的BM25分数
        
        参数:
            query: 查询文本
            
        返回:
            List[float]: 所有文档的BM25分数列表
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        tokenized_query = self.tokenizer(query)
        return self.bm25.get_scores(tokenized_query).tolist()
    
    def search_by_keywords(self, keywords: List[str], n_results: int = 5) -> RetrievalResult:
        """
        基于关键词列表进行检索
        
        参数:
            keywords: 关键词列表
            n_results: 返回结果数量
            
        返回:
            RetrievalResult: 检索结果
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        # 使用关键词列表作为查询
        scores = self.bm25.get_scores(keywords)
        
        # 获取top-k结果
        top_indices = np.argsort(scores)[::-1][:n_results]
        
        result_docs = []
        result_metas = []
        result_scores = []
        result_distances = []
        
        for idx in top_indices:
            if idx < len(self.documents):
                result_docs.append(self.documents[idx])
                result_metas.append(self.metadatas[idx])
                score = scores[idx]
                result_scores.append(score)
                distance = 1.0 / (1.0 + score) if score > 0 else 1.0
                result_distances.append(distance)
        
        return RetrievalResult(
            documents=result_docs,
            metadatas=result_metas,
            distances=result_distances,
            scores=result_scores
        )
