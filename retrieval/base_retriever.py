"""
检索器基类模块

这个模块定义了所有检索器的抽象基类，提供统一的接口和通用功能。
所有具体的检索器实现都必须继承此类并实现必要的抽象方法。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Union
import numpy as np


class RetrievalResult:
    """
    检索结果的标准化数据结构
    
    这个类封装了检索操作的结果，提供统一的数据格式，
    便于不同检索器之间的结果比较和组合。
    """
    
    def __init__(self, 
                 documents: List[str],
                 metadatas: List[Dict[str, Any]],
                 distances: List[float],
                 scores: Optional[List[float]] = None):
        """
        初始化检索结果
        
        参数:
            documents: 检索到的文档列表
            metadatas: 对应的元数据列表
            distances: 距离列表（越小越相似）
            scores: 相似度分数列表（越大越相似），如果未提供则从distances计算
        """
        self.documents = documents
        self.metadatas = metadatas
        self.distances = distances
        self.scores = scores if scores is not None else [1.0 - d for d in distances]
        
        # 验证数据一致性
        assert len(documents) == len(metadatas) == len(distances), \
            "documents, metadatas, and distances must have the same length"
    
    def __len__(self):
        """返回检索结果的数量"""
        return len(self.documents)
    
    def __getitem__(self, index):
        """支持索引访问"""
        return {
            'document': self.documents[index],
            'metadata': self.metadatas[index],
            'distance': self.distances[index],
            'score': self.scores[index]
        }
    
    def to_dict(self):
        """转换为字典格式，兼容现有评估框架"""
        return {
            'documents': self.documents,
            'metadatas': self.metadatas,
            'distances': self.distances,
            'scores': self.scores
        }


class BaseRetriever(ABC):
    """
    检索器的抽象基类
    
    这是所有检索器的基础类，定义了检索的标准接口。
    所有具体的检索器实现都必须继承此类并实现抽象方法。
    
    用途：
    - 为不同的检索策略提供统一的接口
    - 确保所有检索器都实现必要的方法
    - 便于在评估框架中统一处理不同类型的检索器
    - 提供通用的工具方法和验证逻辑
    """
    
    def __init__(self, name: str = None):
        """
        初始化基础检索器
        
        参数:
            name: 检索器名称，用于日志和调试
        """
        self.name = name or self.__class__.__name__
        self._is_built = False
    
    @abstractmethod
    def build_index(self, 
                   documents: List[str], 
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建检索索引
        
        这是抽象方法，必须在子类中实现具体的索引构建逻辑。
        
        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表
            
        注意:
            - 调用此方法后，检索器应该准备好处理查询
            - 实现应该设置 self._is_built = True
        """
        pass
    
    @abstractmethod
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行检索操作
        
        这是抽象方法，必须在子类中实现具体的检索逻辑。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
        """
        pass
    
    def is_built(self) -> bool:
        """
        检查索引是否已构建
        
        返回:
            bool: 如果索引已构建返回True，否则返回False
        """
        return self._is_built
    
    def _validate_built(self):
        """
        验证索引是否已构建，如果未构建则抛出异常
        
        异常:
            RuntimeError: 如果索引未构建
        """
        if not self._is_built:
            raise RuntimeError(f"{self.name} index has not been built. Call build_index() first.")
    
    def _normalize_queries(self, queries: Union[str, List[str]]) -> List[str]:
        """
        标准化查询输入
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            
        返回:
            List[str]: 标准化后的查询列表
        """
        if isinstance(queries, str):
            return [queries]
        return queries
    
    def _normalize_scores(self, scores: List[float]) -> List[float]:
        """
        将分数标准化到[0, 1]范围
        
        参数:
            scores: 原始分数列表
            
        返回:
            List[float]: 标准化后的分数列表
        """
        if not scores:
            return scores
            
        min_score = min(scores)
        max_score = max(scores)
        
        if max_score == min_score:
            return [1.0] * len(scores)
        
        return [(score - min_score) / (max_score - min_score) for score in scores]
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取检索器信息
        
        返回:
            Dict[str, Any]: 包含检索器基本信息的字典
        """
        return {
            'name': self.name,
            'type': self.__class__.__name__,
            'is_built': self._is_built
        }
    
    def __str__(self):
        """字符串表示"""
        return f"{self.name}(built={self._is_built})"
    
    def __repr__(self):
        """详细字符串表示"""
        return f"{self.__class__.__name__}(name='{self.name}', built={self._is_built})"
