"""
稠密检索器模块

这个模块实现了基于向量相似度的稠密检索器，封装了ChromaDB的稠密检索逻辑。
它提供了与现有评估框架完全兼容的接口，同时支持独立使用。
"""

from typing import List, Dict, Any, Union, Optional
from chromadb.api.types import EmbeddingFunction
import chromadb

from .base_retriever import BaseRetriever, RetrievalResult
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function
from chunking_evaluation.utils import generate_db_name, check_db_exists, get_db_path


class DenseRetriever(BaseRetriever):
    """
    持久化稠密检索器

    基于向量相似度的语义检索器，使用ChromaDB作为底层向量数据库。
    支持持久化存储和智能复用机制，大幅提高评估效率。

    主要特点：
    - 语义理解能力强，能处理同义词和语义变体
    - 支持跨语言检索
    - 高效的向量相似度计算
    - 持久化存储，避免重复计算
    - 智能复用机制，自动检测现有数据库
    - 与现有评估框架完全兼容

    持久化功能：
    - 自动根据分块器配置生成数据库名称
    - 智能检测和复用现有向量数据库
    - 统一的存储结构，便于管理
    - 支持多种分块器类型

    使用示例：
        # 自动创建持久化检索器
        retriever = DenseRetriever.create_with_chunker(chunker)

        # 手动指定数据库路径
        retriever = DenseRetriever(db_path="/path/to/db")
    """
    
    def __init__(self,
                 embedding_function: Optional[EmbeddingFunction] = None,
                 db_path: Optional[str] = None,
                 collection_name: str = "dense_retrieval",
                 search_ef: int = 50,
                 name: str = None):
        """
        初始化稠密检索器

        参数:
            embedding_function: 嵌入函数，如果未提供则使用默认的OpenAI嵌入函数
            db_path: ChromaDB数据库路径，如果为None则使用默认路径
            collection_name: 集合名称
            search_ef: HNSW搜索参数，影响检索质量和速度的平衡
            name: 检索器名称
        """
        super().__init__(name or "DenseRetriever")

        # 设置嵌入函数
        self.embedding_function = embedding_function or get_openai_embedding_function()

        # 设置持久化数据库路径
        self.db_path = db_path
        self.collection_name = collection_name
        self.search_ef = search_ef
        self.client = None
        self.collection = None
        
    def build_index(self,
                   documents: List[str],
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建稠密检索索引

        使用提供的文档和元数据创建ChromaDB集合，并计算文档的向量表示。

        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表

        异常:
            ValueError: 如果documents和metadatas长度不匹配
        """
        if len(documents) != len(metadatas):
            raise ValueError("documents and metadatas must have the same length")

        # 创建持久化ChromaDB客户端
        if self.db_path:
            self.client = chromadb.PersistentClient(path=self.db_path)
        else:
            # 如果没有指定路径，使用内存客户端
            self.client = chromadb.Client()

        # 创建或获取集合
        try:
            self.collection = self.client.get_collection(
                name=self.collection_name,
                embedding_function=self.embedding_function
            )
            print(f"🔄 加载现有集合: {self.collection_name}")
        except Exception:
            # 集合不存在，创建新集合
            try:
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    embedding_function=self.embedding_function
                )
                print(f"🆕 创建新集合: {self.collection_name}")
            except Exception as e:
                if "already exists" in str(e):
                    # 集合已存在，直接获取
                    self.collection = self.client.get_collection(
                        name=self.collection_name,
                        embedding_function=self.embedding_function
                    )
                    print(f"🔄 获取已存在集合: {self.collection_name}")
                else:
                    raise e

        # 检查集合是否已有数据
        existing_count = self.collection.count()
        if existing_count > 0:
            print(f"📊 集合已包含 {existing_count} 个文档，跳过数据添加")
            self._is_built = True
            return

        # 批量添加文档，避免一次性添加过多数据
        BATCH_SIZE = 100  # ChromaDB可以处理更大的批次
        for i in range(0, len(documents), BATCH_SIZE):
            batch_docs = documents[i:i+BATCH_SIZE]
            batch_metas = metadatas[i:i+BATCH_SIZE]
            batch_ids = [str(j) for j in range(i, i+len(batch_docs))]

            self.collection.add(
                documents=batch_docs,
                metadatas=batch_metas,
                ids=batch_ids
            )

        print(f"💾 已添加 {len(documents)} 个文档到集合")
        self._is_built = True
    
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行稠密检索
        
        使用向量相似度检索最相关的文档。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        # 标准化查询输入
        query_list = self._normalize_queries(queries)
        is_single_query = isinstance(queries, str)
        
        # 执行向量检索
        results = self.collection.query(
            query_texts=query_list,
            n_results=n_results
        )
        
        # 转换为RetrievalResult格式
        retrieval_results = []
        for i in range(len(query_list)):
            # 获取当前查询的结果
            docs = results['documents'][i] if i < len(results['documents']) else []
            metas = results['metadatas'][i] if i < len(results['metadatas']) else []
            distances = results['distances'][i] if i < len(results['distances']) else []
            
            # 创建RetrievalResult对象
            retrieval_result = RetrievalResult(
                documents=docs,
                metadatas=metas,
                distances=distances
            )
            retrieval_results.append(retrieval_result)
        
        # 根据输入类型返回相应格式
        if is_single_query:
            return retrieval_results[0]
        return retrieval_results
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息

        返回:
            Dict[str, Any]: 包含集合详细信息的字典
        """
        if not self._is_built:
            return {"error": "Index not built"}

        try:
            document_count = self.collection.count()
            # 安全获取embedding函数名称
            embedding_name = getattr(self.embedding_function, 'name',
                                   getattr(self.embedding_function, '__class__', {}).get('__name__', 'Unknown'))

            return {
                "collection_name": self.collection_name,
                "document_count": document_count,
                "embedding_function": embedding_name,
                "search_ef": self.search_ef,
                "db_path": self.db_path,
                "is_persistent": self.db_path is not None
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取检索器详细信息
        
        返回:
            Dict[str, Any]: 包含检索器和集合信息的字典
        """
        base_info = super().get_info()
        collection_info = self.get_collection_info()

        # 安全获取embedding函数名称
        embedding_name = getattr(self.embedding_function, 'name',
                               getattr(self.embedding_function, '__class__', {}).get('__name__', 'Unknown'))

        return {
            **base_info,
            "embedding_function": embedding_name,
            "collection_info": collection_info
        }
    
    @classmethod
    def create_with_chunker(cls,
                           chunker,
                           embedding_function: Optional[EmbeddingFunction] = None,
                           name: str = None) -> 'DenseRetriever':
        """
        根据分块器创建持久化稠密检索器

        这个类方法根据分块器的类型和参数自动生成数据库路径，
        并创建相应的持久化检索器实例。

        参数:
            chunker: 分块器实例，用于生成数据库名称
            embedding_function: 嵌入函数，如果未提供则使用默认的OpenAI嵌入函数
            name: 检索器名称

        返回:
            DenseRetriever: 配置好的持久化稠密检索器实例
        """
        # 生成数据库名称和路径
        db_name = generate_db_name(chunker)
        db_path = get_db_path(db_name)

        # 检查数据库是否已存在
        db_exists = check_db_exists(db_name)
        if db_exists:
            print(f"🔄 检测到现有数据库，正在加载: {db_name}")
        else:
            print(f"🆕 创建新的向量数据库: {db_name}")
            print(f"💾 数据库将保存到: {db_path}")

        # 创建检索器实例
        return cls(
            embedding_function=embedding_function,
            db_path=db_path,
            collection_name="chunks",
            name=name or f"DenseRetriever_{db_name}"
        )
