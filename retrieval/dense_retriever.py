"""
稠密检索器模块

这个模块实现了基于向量相似度的稠密检索器，封装了ChromaDB的稠密检索逻辑。
它提供了与现有评估框架完全兼容的接口，同时支持独立使用。
"""

import numpy as np
from typing import List, Dict, Any, Union, Optional
from chromadb.api.types import EmbeddingFunction

from .base_retriever import BaseRetriever, RetrievalResult
from chunking_evaluation.evaluation_framework.llm_config import get_openai_embedding_function


class DenseRetriever(BaseRetriever):
    """
    稠密检索器
    
    基于向量相似度的语义检索器，使用ChromaDB作为底层向量数据库。
    支持多种嵌入模型，能够捕获文本的语义相似性。
    
    特点：
    - 语义理解能力强，能处理同义词和语义变体
    - 支持跨语言检索
    - 高效的向量相似度计算
    - 与现有评估框架完全兼容
    """
    
    def __init__(self, 
                 embedding_function: Optional[EmbeddingFunction] = None,
                 collection_name: str = "dense_retrieval",
                 search_ef: int = 50,
                 name: str = None):
        """
        初始化稠密检索器
        
        参数:
            embedding_function: 嵌入函数，如果未提供则使用默认的OpenAI嵌入函数
            chroma_db_path: ChromaDB数据库路径，如果为None则使用内存数据库
            collection_name: 集合名称
            search_ef: HNSW搜索参数，影响检索质量和速度的平衡
            name: 检索器名称
        """
        super().__init__(name or "DenseRetriever")
        
        # 设置嵌入函数
        self.embedding_function = embedding_function or get_openai_embedding_function()
        
        # 使用纯内存集合（移除持久化客户端）
        self.collection_name = collection_name
        self.search_ef = search_ef
        self.collection = None
        
    def build_index(self, 
                   documents: List[str], 
                   metadatas: List[Dict[str, Any]]) -> None:
        """
        构建稠密检索索引
        
        使用提供的文档和元数据创建ChromaDB集合，并计算文档的向量表示。
        
        参数:
            documents: 要索引的文档列表
            metadatas: 对应的元数据列表
            
        异常:
            ValueError: 如果documents和metadatas长度不匹配
        """
        if len(documents) != len(metadatas):
            raise ValueError("documents and metadatas must have the same length")
        
        # 以最简单的方式模拟内存集合
        self.collection = _InMemoryDenseCollection(self.embedding_function, self.search_ef)
        
        # 批量添加文档，避免一次性添加过多数据
        BATCH_SIZE = 10  # ChromaDB API限制
        for i in range(0, len(documents), BATCH_SIZE):
            batch_docs = documents[i:i+BATCH_SIZE]
            batch_metas = metadatas[i:i+BATCH_SIZE]
            batch_ids = [str(j) for j in range(i, i+len(batch_docs))]
            
            self.collection.add(
                documents=batch_docs,
                metadatas=batch_metas,
                ids=batch_ids
            )
        
        self._is_built = True
    
    def retrieve(self, 
                queries: Union[str, List[str]], 
                n_results: int = 5) -> Union[RetrievalResult, List[RetrievalResult]]:
        """
        执行稠密检索
        
        使用向量相似度检索最相关的文档。
        
        参数:
            queries: 查询文本，可以是单个字符串或字符串列表
            n_results: 每个查询返回的结果数量
            
        返回:
            如果输入是单个查询，返回单个RetrievalResult
            如果输入是查询列表，返回RetrievalResult列表
            
        异常:
            RuntimeError: 如果索引未构建
        """
        self._validate_built()
        
        # 标准化查询输入
        query_list = self._normalize_queries(queries)
        is_single_query = isinstance(queries, str)
        
        # 执行向量检索
        results = self.collection.query(query_list, n_results)
        
        # 转换为RetrievalResult格式
        retrieval_results = []
        for i in range(len(query_list)):
            # 获取当前查询的结果
            docs = results['documents'][i] if i < len(results['documents']) else []
            metas = results['metadatas'][i] if i < len(results['metadatas']) else []
            distances = results['distances'][i] if i < len(results['distances']) else []
            
            # 创建RetrievalResult对象
            retrieval_result = RetrievalResult(
                documents=docs,
                metadatas=metas,
                distances=distances
            )
            retrieval_results.append(retrieval_result)
        
        # 根据输入类型返回相应格式
        if is_single_query:
            return retrieval_results[0]
        return retrieval_results
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息
        
        返回:
            Dict[str, Any]: 包含集合详细信息的字典
        """
        if not self._is_built:
            return {"error": "Index not built"}
        
        try:
            collection_data = self.collection.get()
            return {
                "collection_name": self.collection_name,
                "document_count": len(collection_data['documents']),
                "embedding_function": self.embedding_function.__class__.__name__,
                "search_ef": self.search_ef
            }
        except Exception as e:
            return {"error": str(e)}
    
    def get_info(self) -> Dict[str, Any]:
        """
        获取检索器详细信息
        
        返回:
            Dict[str, Any]: 包含检索器和集合信息的字典
        """
        base_info = super().get_info()
        collection_info = self.get_collection_info()
        
        return {
            **base_info,
            "embedding_function": self.embedding_function.__class__.__name__,
            "collection_info": collection_info
        }
    
    def save_index(self, path: str) -> None:
        raise NotImplementedError("Persistent saving removed; use in-memory retrieval only.")
    
    @classmethod
    def load_index(cls, path: str, collection_name: str = "dense_retrieval", embedding_function: Optional[EmbeddingFunction] = None) -> 'DenseRetriever':
        raise NotImplementedError("Persistent loading removed; use in-memory retrieval only.")


class _InMemoryDenseCollection:
    def __init__(self, embedding_function, search_ef: int = 50):
        self.embedding_function = embedding_function
        self.search_ef = search_ef
        self._documents: List[str] = []
        self._metadatas: List[Dict[str, Any]] = []
        self._embeddings: Optional[np.ndarray] = None

    def add(self, documents: List[str], metadatas: List[Dict[str, Any]], ids: List[str]):
        self._documents.extend(documents)
        self._metadatas.extend(metadatas)
        # 增量计算嵌入
        new_embs = np.array(self.embedding_function(documents))
        if self._embeddings is None:
            self._embeddings = new_embs
        else:
            self._embeddings = np.vstack([self._embeddings, new_embs])

    def query(self, query_texts: List[str], n_results: int):
        if self._embeddings is None or len(self._documents) == 0:
            return {"documents": [[] for _ in query_texts], "metadatas": [[] for _ in query_texts], "distances": [[] for _ in query_texts]}

        # 计算查询向量
        q_embs = np.array(self.embedding_function(query_texts))
        # 余弦距离（1 - 余弦相似度）
        doc_norms = np.linalg.norm(self._embeddings, axis=1, keepdims=True) + 1e-8
        q_norms = np.linalg.norm(q_embs, axis=1, keepdims=True) + 1e-8
        normalized_docs = self._embeddings / doc_norms
        normalized_qs = q_embs / q_norms
        sims = normalized_qs @ normalized_docs.T
        dists = 1.0 - sims

        results = {"documents": [], "metadatas": [], "distances": []}
        for i in range(dists.shape[0]):
            order = np.argsort(dists[i])[:n_results]
            results["documents"].append([self._documents[j] for j in order])
            results["metadatas"].append([self._metadatas[j] for j in order])
            results["distances"].append([float(dists[i, j]) for j in order])
        return results

    def get(self, include: List[str]):
        data = {}
        if 'documents' in include:
            data['documents'] = self._documents
        if 'metadatas' in include:
            data['metadatas'] = self._metadatas
        return data
