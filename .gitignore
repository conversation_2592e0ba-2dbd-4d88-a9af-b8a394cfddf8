# Ignore egg-info directory
*.egg-info
__pycache__/
**/__pycache__/

# macOS system files
.DS_Store
**/.DS_Store
.AppleDouble
.LSOverride

# Python
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# User custom ignore
.kiro/
.codebuddy/

test/